# Enhanced Security Configuration for Quotation Pro

# Disable directory browsing and prevent server signature
Options +FollowSymLinks -Indexes -MultiViews
ServerSignature Off

# Enable Rewrite Engine
RewriteEngine On

# Force HTTPS - uncomment if you have SSL certificate
# RewriteCond %{HTTPS} off
# RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Block crawlers and bots
<IfModule mod_headers.c>
    Header set X-Robots-Tag "noindex, nofollow, noarchive, nosnippet, noodp, noydir, noimageindex"
</IfModule>

# Security: Limit request methods
<IfModule mod_authz_core.c>
    # Apache 2.4 syntax
    <LimitExcept GET POST HEAD OPTIONS>
        Require all denied
    </LimitExcept>
</IfModule>

<IfModule !mod_authz_core.c>
    # Apache 2.2 syntax (for backward compatibility)
    <LimitExcept GET POST HEAD OPTIONS>
        Order deny,allow
        Deny from all
    </LimitExcept>
</IfModule>

# Protect against SQL Injection and XSS
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_METHOD} ^(HEAD|TRACE|DELETE|TRACK|DEBUG) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# RESTRICT ACCESS TO ONLY BACKEND, API, AND UPLOADS
# Block access to everything except allowed directories
RewriteCond %{REQUEST_URI} !^/(backend|api|uploads)/ [NC]
RewriteCond %{REQUEST_URI} !^/(backend|api|uploads)$ [NC]
RewriteRule ^ - [F,L]

# If the request is for an actual file or directory, skip all rewrite rules
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# Resolve backend requests
RewriteCond %{REQUEST_URI} ^/backend
RewriteRule ^backend/(.*)$ backend/web/$1 [L]

# Resolve API requests
RewriteCond %{REQUEST_URI} ^/api
RewriteRule ^api/(.*)$ api/web/$1 [L]

# Handle uploads directory directly
RewriteCond %{REQUEST_URI} ^/uploads
RewriteRule ^uploads/(.*)$ uploads/$1 [L]
