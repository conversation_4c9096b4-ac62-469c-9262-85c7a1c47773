<?php

namespace api\modules\v1\controllers;

use common\models\Business;
use common\models\enum\Key;
use common\models\Identity;
use common\models\User;
use common\models\UserDevice;
use Yii;
use yii\base\Model;
use yii\db\Expression;
use yii\filters\Cors;
use yii\helpers\Json;
use yii\rest\Controller;
use yii\web\Response;


class BaseApiController extends Controller
{

    const active = 'active';
    const inactive = 'inactive';
    const blocked = 'blocked';
    const deleted = 'deleted';

    /**
     * Key which has to be in HTTP USERNAME and PASSWORD headers
     */
    const APPLICATION_ID = 'ASCCPE';
    /** @var Identity $identity */

    public $isFromApi = true;
    public $success;
    public $errorCode;
    public $data;
    public $message;

    public $identity;
    /**
     * @var Business $business ;
     */
    public $business;
    public $device;

    public $startTime;

    /**
     * @var int[]
     */
    public $logUserIds;

    /**
     * @apiDefine AUTH
     * @apiHeader {String} AUTH_TOKEN Users unique access-key.
     * @apiParam {integer="en","ar"} language
     *
     * @apiHeaderExample {json} Header Example:
     *     {
     *       "AUTH_TOKEN": "[HASH]"
     *     }
     */

    /**
     * @apiDefine Response
     *
     * @apiSuccess {String} message success message
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "errorStatus" : false,
     *       "message" : "success message",
     *       "data" : {
     *          {"key" : "value"},
     *       }
     *     }
     *
     * @apiErrorExample Error-Response:
     *     HTTP/1.1 200 ok
     *     {
     *       "errorStatus": true,
     *       "code": error code,
     *       "message": "error message"
     *     }
     */

    // Members
    /** @var User $user */
    public $user;
    private $format = 'json';
    public bool $disableNumericCheck = false;


    function __construct($id, $module, $config = [])
    {
        parent::__construct($id, $module, $config);
        if ($module === null) {
            $this->isFromApi = false;
            Yii::$app->request->setBodyParams(null);
        }
    }

    public function behaviors()
    {
        $behaviors = parent::behaviors();

        // CORS filter must be applied first to handle preflight requests
        $behaviors['corsFilter'] = [
            'class' => Cors::class,
            'cors' => [
                // Wildcard origin allowed when credentials=false
                'Origin' => ['*'],
                'Access-Control-Allow-Methods' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
                'Access-Control-Allow-Headers' => [
                    'Content-Type',
                    'Authorization',
                    'AUTH_TOKEN',
                    'X-Requested-With',
                    'Accept',
                    'Origin',
                    'Cache-Control',
                    'X-File-Name',
                    'X-HTTP-Method-Override',
                    'UUID' // For device identification
                ],
                'Access-Control-Allow-Credentials' => false, // Must be false when using wildcard origin
                'Access-Control-Max-Age' => 86400, // 24 hours
                'Access-Control-Expose-Headers' => [
                    'X-Pagination-Per-Page',
                    'X-Pagination-Total-Count',
                    'X-Pagination-Current-Page',
                    'X-Pagination-Page-Count',
                    'X-Rate-Limit-Limit',
                    'X-Rate-Limit-Remaining',
                    'X-Rate-Limit-Reset',
                    'AUTH_TOKEN',
                    'Content-Disposition' // For file downloads
                ],
            ]
        ];

        return $behaviors;
    }


    public function init()
    {
        parent::init();
        $this->startTime = microtime(true);
        $this->logUserIds = env(logUserIds) ?? [];
        $this->logRequest();
        if (env(Key::MAINTENANCE_MODE)) {
            return $this->_sendErrorResponse(200, "Application is under maintenance mode! Please wait for 1 hour and try again!", 503); // Server maintenance
        }
    }

    /**
     * Default response format
     * either 'json' or 'xml'
     */
    public function actionError()
    {
        $return_value = array("status" => false, "message" => "Action not found.");
        return $this->_sendResponse(200, '{ "d" :' . json_encode($return_value) . '}');
    }

    protected function logExecutionTime()
    {
        $endTime = microtime(true);
        return secondsToTime($endTime - $this->startTime);
    }

    /**
     * Action to check server reachability (health check)
     * @return array
     */
    public function actionHealthCheck()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'status' => 'OK',
        ];
    }

    /**
     * @param mixed $body
     * @param string $message
     * @param int $http_status
     * @return true|void
     */
    protected function _sendResponse(mixed $body = [], $message = '', $http_status = 200)
    {
        $this->success = true;
        $this->data = $body;
        $this->message = $message;

        if ($this->isFromApi) {
            $content_type = 'text/json';
            $status_header = 'HTTP/1.1 ' . $http_status . ' ' . $this->_getStatusCodeMessage($http_status);
            Yii::$app->response->format = Response::FORMAT_JSON;
            Yii::$app->response->statusCode = $http_status;
            Yii::$app->response->headers->set('Content-Type', 'application/json');
            $this->response = [
                "success" => true,
                "data" => $body,
                "timestamp" => time(),
                "executionTime" => $this->logExecutionTime(),
                'message' => $message,
            ];
            $this->logResponse($this->response);
            Yii::$app->response->data = $this->response;
            Yii::$app->response->send();
            exit();
        }
        return $this->success;
    }

    /**
     * Gets the message for a status code
     *
     * @param mixed $status
     * @access private
     * @return string
     */
    protected function _getStatusCodeMessage($status)
    {
// these could be stored in a .ini file and loaded
// via parse_ini_file()... however, this will suffice
// for an example
        $codes = array(
            100 => 'Continue',
            101 => 'Switching Protocols',
            200 => 'OK',
            201 => 'Created',
            202 => 'Accepted',
            203 => 'Non-Authoritative Information',
            204 => 'No Content',
            205 => 'Reset Content',
            206 => 'Partial Content',
            300 => 'Multiple Choices',
            301 => 'Moved Permanently',
            302 => 'Found',
            303 => 'See Other',
            304 => 'Not Modified',
            305 => 'Use Proxy',
            306 => '(Unused)',
            307 => 'Temporary Redirect',
            400 => 'Bad Request',
            401 => 'Unauthorized',
            402 => 'Payment Required',
            403 => 'Forbidden',
            404 => 'Not Found',
            405 => 'Method Not Allowed',
            406 => 'Not Acceptable',
            407 => 'Proxy Authentication Required',
            408 => 'Request Timeout',
            409 => 'Conflict',
            410 => 'Gone',
            411 => 'Length Required',
            412 => 'Precondition Failed',
            413 => 'Request Entity Too Large',
            414 => 'Request-URI Too Long',
            415 => 'Unsupported Media Type',
            416 => 'Requested Range Not Satisfiable',
            417 => 'Expectation Failed',
            500 => 'Internal Server Error',
            501 => 'Not Implemented',
            502 => 'Bad Gateway',
            503 => 'Service Unavailable',
            504 => 'Gateway Timeout',
            505 => 'HTTP Version Not Supported'
        );
        return (isset($codes[$status])) ? $codes[$status] : '';
    }

    public function render($view, $params = [])
    {
        return parent::render($view, $params); // TODO: Change the autogenerated stub
    }

    function __clone()
    {
        // TODO: Implement __clone() method.
    }

    // helper functions

    /**
     * @return array action filters
     */

    public function filters()
    {
        return array();
    }

    /**
     * @param string $name
     * @param array $params
     * @return mixed
     */
    public function __call($name, $params)
    {
        return parent::__call($name, $params); // TODO: Change the autogenerated stub
    }

    public function fillNullArr($value)
    {

        return array_replace($value, array_fill_keys(
                array_keys($value, null), ""
            )
        );
    }

    /**
     * @param int $status
     * @param string $filePath
     * @param string $content_type
     */
    protected function _sendFileResponse($status = 200, $filePath = '', $content_type = 'application/pdf')
    {
        if (!empty($filePath)) {
            header("Content-type:$content_type");
            header('Content-Disposition: attachment; filename="' . basename($filePath) . '"');
            header('Content-Length: ' . filesize($filePath));
            readfile($filePath);
            exit(0);
//            Yii::$app->end($status, false);
        }
    }

    /**
     * @param array $body
     * @param string $message
     * @param int $http_status
     */
    protected function _logResponse($body = [], $message = '', $http_status = 200)
    {
        $content_type = 'text/json';
        $status_header = 'HTTP/1.1 ' . $http_status . ' ' . $this->_getStatusCodeMessage($http_status);
        header($status_header);
        header('Content-type: ' . $content_type);

        if (!is_array($body)) {
            $body = array('message' => $body);
            $body = array_merge(array("status" => true), $body);
        }

        $body = Json::encode([
            "errorStatus" => false,
            "data" => $body,
            "timestamp" => time(),
            'message' => $message
        ]);

        Yii::getLogger()->log(json_encode($body), 'log');
    }

    /**
     * @return User
     */
    protected function _checkAdminAccess()
    {
        return $this->_checkAuth();
    }

    protected function _checkAuthSafely()
    {
        if (isset($_SERVER['HTTP_AUTH_TOKEN'])) {
            return $this->_checkAuth(false);
        }
        return null;
    }

    protected function _checkAuth($strictCheck = true)
    {
        if (!$this->isFromApi) {
            $this->user = Yii::$app->user;
            return $this->user;
        }

        if (!isset($_SERVER['HTTP_AUTH_TOKEN'])) {
            // Error: Unauthorized
            return $this->_sendErrorResponse(401);
        }

        $token = $_SERVER['HTTP_AUTH_TOKEN'];
        //  $this->user = User::findIdentityByAccessToken($token);
        $this->identity = Identity::findIdentityByAccessToken($token);

        if ($this->identity == false) {
            if ($strictCheck) {
                return $this->_sendErrorResponse(401);
            }
            return null;
        }
        Yii::$app->user->identity = $this->identity->getUser();
        if ($this->identity == null || $this->identity->getUser()->isRemoved()) {
            return $this->_sendErrorResponse(401);
        }
        $this->user = $this->identity->getUser();
        $this->business = $this->user->business;
        return $this->user;
    }

    protected function _logUserActiveTime()
    {
        if (!isset($_SERVER['HTTP_UUID'])) {
            // Error: Unauthorized
            return 0;
        }
        $deviceId = $_SERVER['HTTP_UUID'];
        $userDevice = UserDevice::findByDeviceId($deviceId);
        if ($userDevice === null) {
            return 0;
        }
        $userDevice->updatedAt = new Expression("Now()");
        $userDevice->save(false);
        $this->device = $userDevice;
        $this->_checkUsage();
        return $userDevice;
    }

    private function _checkUsage()
    {
        try {
            /** @var User $user */
            $user = $this->_checkAuthSafely();
            if ($user == null) {
                return 0;
            }
            $userLevel = $user->getFcmLevel();
            if ($userLevel != null && $userLevel >= 4) {
                $this->business->checkAndSendUsageLimitNotification();
            }
        } catch (\Exception $e) {
            logError($e->getMessage() . "\n" . $e->getTraceAsString());
        }
    }

    /**
     * @param int $status
     * @param mixed $customMessage
     * @param mixed $errorCode
     * @param string $contentType
     */
    protected function _sendErrorResponse($status = 400, $customMessage = null, $errorCode = null, $contentType = 'text/json')
    {
        $message = $this->_getStatusCodeMessage($status);

        if ($customMessage !== null) {
            $message .= ". " . $customMessage;
        }

        if ($errorCode === null) {
            $code = $status;
        } else {
            $message = $customMessage;
            $code = $errorCode;
        }

        $this->success = false;
        $this->errorCode = $code;
        $this->message = $message;

        if ($this->isFromApi) {
            $status_header = 'HTTP/1.1 ' . $status . ' ' . $this->_getStatusCodeMessage($status);
            Yii::$app->response->format = Response::FORMAT_JSON;
            Yii::$app->response->statusCode = $status;
            Yii::$app->response->headers->set('Content-Type', 'application/json');
            $body = [
                "success" => false,
                "code" => $code,
                'message' => $message
            ];

            if ($_REQUEST['debug'] ?? false){
                Yii::error("code: $code \n message: $message \n trace \n");
            }

            if (env(Key::LOG_VALIDATION_ERRORS)) {
                $skipErrorCodes = env(Key::SKIP_ERROR_CODES);
                if (!in_array($code, $skipErrorCodes, true)) {
                    Yii::error("code: $code \n message: $message \n trace \n");
                }
            }

            Yii::getLogger()->log("code: $code, message: $message \n trace \n" , 'error');
            Yii::$app->response->data = $body;
            Yii::$app->response->send();
            exit();
        }
        return $this->success;
    }

    protected function _checkEmail()
    {
        if (!isset($this->user)) {
            // Error: Unauthorized
            return $this->_sendErrorResponse(401);
        }
        if (!$this->user->isActive()) {
            return $this->_sendErrorResponse(403); //"To enjoy the full ViRVi experience, please activate your account by clicking on the link sent to your email address:"
        }
        return true;
    }

    protected function isEmail($email)
    {
        if (preg_match("/^([a-zA-Z0-9])+([a-zA-Z0-9\._-])*@([a-zA-Z0-9_-])+([a-zA-Z0-9\._-]+)+$/", $email)) {
            return true;
        }
        return false;
    }

    protected function _getUserID()
    {
        $token = $_SERVER['HTTP_AUTH_TOKEN'];

        return $this->_getTokenCache($token);
    }

    protected function _getTokenCache($token)
    {
        $cache = Yii::$app->cache;
        return $cache->get($token);
    }

    protected function _userData(Model $user, $withBusiness = true)
    {
        $data = $user->toArray();
//        TODO: remove below line after android app update!
        $withBusiness = true;
        unset($data['auth_key']);
        unset($data['accessToken']);
        unset($data['password_hash']);
        unset($data['password_reset_token']);
        unset($data['otpExpiry']);
        if ($user instanceof User && $withBusiness) {
            $data['business'] = $user->business != null ? $user->business : null;
            if ($this->business == null) {
                $this->business = $user->business;
            }
            if ($data['business']) {
                $data['businessStats'] = $user->business->businessStats;
                $data['businessSettings'] = $user->business->businessSettings;
//                $data['defaultSettings'] = $user->business->settings();
                $data['subscriptions'] = $user->business->activeSubscriptions;
            }
        }
        return $data;
    }

    protected function _saveTokenCache($id)
    {
        $cache = Yii::$app->cache;
        $token = $this->_generateToken();
        $cache->set($token, $id, 0);
        return $token;
    }

    /**
     * Generates new token
     */
    protected function _generateToken()
    {
        return Yii::$app->security->generateRandomString() . '_' . time();
    }

    protected function _getToken()
    {
        return $_SERVER['HTTP_AUTH_TOKEN'];
    }

    protected function convertToArray($objectList)
    {
        $response = array();

        foreach ($objectList as $object) {
            $response[] = $object;
        }
        return $response;
    }

    protected function processRequestParams()
    {
        $postData = $request = Yii::$app->request->post();
        $data = [];
        foreach ($postData as $key => $value) {
            // $arr[3] will be updated with each value from $arr...
            list ($tbl, $newKey) = explode('.', $key);
            if ($tbl & $newKey) {
                $data[$tbl][$newKey] = $value;
            }

            print_r($data);
        }
    }

    private function logRequest()
    {
        /** @var User $user */
        $user = $this->_checkAuthSafely();
        if ($user == null) {
            return 0;
        }
        if (in_array($this->user->id, $this->logUserIds)) {
            logMail("API Request | userId - {$this->user->id}", [
                'api' => $_SERVER['REQUEST_URI'],
                'userId' => $this->user->id,
                'businessId' => $this->business->id,
                'POST' => "\n\n" . json_encode($this->request->post(), JSON_PRETTY_PRINT) . "\n\n",
                'GET' => "\n\n" . json_encode($this->request->get(), JSON_PRETTY_PRINT) . "\n\n",
                '$_SERVER' => $_SERVER,
                'apiURL' => getCurrentUrl(),
            ],
                "logRequest",
                env(logUserIdsEmail) ?? null,
            );
        }
    }

    private function logResponse($response)
    {
        /** @var User $user */
        $user = $this->_checkAuthSafely();
        if ($user == null) {
            return 0;
        }
        if (in_array($this->user->id, $this->logUserIds)) {

            if (!env(logUserIdsResponse)) {
                $response = "logUserIdsResponse is disabled!";
            }
            if (isset($response['data']['pdfFileBase64'])) {
                $response['data']['pdfFileBase64'] = "Your PDF BASE64 DATA!";
            }
            logMail("API Response | userId - {$this->user->id}", [
                'api' => $_SERVER['REQUEST_URI'],
                'userId' => $this->user->id,
                'businessId' => $this->business->id,
                '$_RESPONSE' => "\n\n" . Json::encode($response, JSON_PRETTY_PRINT | 320) . "\n\n",
                'POST' => "\n\n" . json_encode($this->request->post(), JSON_PRETTY_PRINT) . "\n\n",
                'GET' => "\n\n" . json_encode($this->request->get(), JSON_PRETTY_PRINT) . "\n\n",
                '$_SERVER' => $_SERVER,
                'apiURL' => getCurrentUrl(),
            ],
                "logResponse",
                env(logUserIdsEmail) ?? null,
            );
        }
    }
}
