# API Security Configuration
RewriteEngine on

# Disable directory browsing
Options -Indexes

# CORS headers are handled by the Yii2 application (BaseApiController)
# Removed conflicting CORS configuration to prevent header duplication

# If a directory or a file exists, use the request directly
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
# Otherwise forward the request to index.php
RewriteRule ^(.*)\?*$ index.php?r=$1 [L,QSA]
