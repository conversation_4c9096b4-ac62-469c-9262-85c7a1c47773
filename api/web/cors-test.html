<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test for Quotation Pro API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>CORS Test for Quotation Pro API</h1>
    
    <div class="test-section info">
        <h3>Instructions</h3>
        <p>This page tests CORS functionality for the Quotation Pro API. Click the buttons below to test different scenarios.</p>
        <p><strong>Note:</strong> Make sure to serve this file from a different origin than your API (e.g., different port or domain) to properly test CORS.</p>
    </div>

    <div class="test-section">
        <h3>Test 1: Simple GET Request (Health Check)</h3>
        <button onclick="testSimpleGet()">Test Simple GET</button>
        <div id="simple-get-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Preflight Request (POST with Custom Headers)</h3>
        <button onclick="testPreflightRequest()">Test Preflight Request</button>
        <div id="preflight-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Request with Credentials</h3>
        <button onclick="testWithCredentials()">Test With Credentials</button>
        <div id="credentials-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 4: Custom Headers Test</h3>
        <button onclick="testCustomHeaders()">Test Custom Headers</button>
        <div id="custom-headers-result"></div>
    </div>

    <script>
        // Configure your API base URL here
        const API_BASE_URL = window.location.origin + '/v1';
        
        function displayResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = success ? 'success' : 'error';
            element.innerHTML = `
                <h4>${success ? '✅ Success' : '❌ Error'}</h4>
                <p>${message}</p>
                ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
        }

        async function testSimpleGet() {
            try {
                const response = await fetch(`${API_BASE_URL}/base-api/health-check`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displayResult('simple-get-result', true, 'Simple GET request successful', data);
                } else {
                    displayResult('simple-get-result', false, `Request failed with status: ${response.status}`);
                }
            } catch (error) {
                displayResult('simple-get-result', false, `CORS Error: ${error.message}`);
            }
        }

        async function testPreflightRequest() {
            try {
                const response = await fetch(`${API_BASE_URL}/base-api/health-check`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'AUTH_TOKEN': 'test-token'
                    },
                    body: JSON.stringify({ test: 'data' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displayResult('preflight-result', true, 'Preflight request successful', data);
                } else {
                    displayResult('preflight-result', false, `Request failed with status: ${response.status}`);
                }
            } catch (error) {
                displayResult('preflight-result', false, `CORS Error: ${error.message}`);
            }
        }

        async function testWithCredentials() {
            try {
                const response = await fetch(`${API_BASE_URL}/base-api/health-check`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displayResult('credentials-result', true, 'Request with credentials successful', data);
                } else {
                    displayResult('credentials-result', false, `Request failed with status: ${response.status}`);
                }
            } catch (error) {
                displayResult('credentials-result', false, `CORS Error: ${error.message}`);
            }
        }

        async function testCustomHeaders() {
            try {
                const response = await fetch(`${API_BASE_URL}/base-api/health-check`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'AUTH_TOKEN': 'test-token',
                        'UUID': 'test-device-uuid',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displayResult('custom-headers-result', true, 'Custom headers request successful', data);
                } else {
                    displayResult('custom-headers-result', false, `Request failed with status: ${response.status}`);
                }
            } catch (error) {
                displayResult('custom-headers-result', false, `CORS Error: ${error.message}`);
            }
        }

        // Display current origin for reference
        document.addEventListener('DOMContentLoaded', function() {
            const infoSection = document.querySelector('.info');
            infoSection.innerHTML += `<p><strong>Current Origin:</strong> ${window.location.origin}</p>`;
            infoSection.innerHTML += `<p><strong>API Base URL:</strong> ${API_BASE_URL}</p>`;
        });
    </script>
</body>
</html>
