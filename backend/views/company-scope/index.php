<?php

use yii\grid\GridView;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\TermsConditionSearch */
/* @var $model backend\models\TermsConditionSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Company Scope';
$this->params['breadcrumbs'][] = $this->title;
$date = !empty($custom_data['date_range']) ? $custom_data['date_range'] : null;
$action_list = '';
$visible = true;
$session = Yii::$app->session;
$isAdmin = Yii::$app->session['isAdmin'];

if ($session['isAdmin'] == 1) {
    $action_list = '{update} {delete}';
} else {
    $action_list = '{view}';
    $visible = false;
}
$showDownloadOptions = false;

?>
<div class="terms-condition-index">
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <?php $form = ActiveForm::begin(['method' => 'get']); ?>
                            <div class="row md-form">
                                <div class="col-md-4 col-lg-4 col-sm-4">
                                    <?= $form->field($model, 'DateRange')->textInput(['class' => 'form-control', 'tabindex' => '1', 'autocomplete' => "off", 'id' => 'daterange', 'value' => $date])->label('Date Range'); ?>
                                </div>
                                <div class="form-group">
                                    <?= Html::submitButton(Yii::t('app', 'Submit'), ['class' => 'btn btn-primary', 'name' => 'btnReport', 'value' => 'showreport']) ?>
                                    <?php if ($showDownloadOptions && $isAdmin): ?>
                                        <?= Html::submitButton(Yii::t('app', 'Download Excel'), ['class' => 'btn btn-primary', 'name' => 'btnReport', 'value' => 'excel']) ?>
                                        <?= Html::submitButton(Yii::t('app', 'Download Pdf'), ['class' => 'btn btn-primary', 'name' => 'btnReport', 'value' => 'pdf', 'formtarget' => '_blank']) ?>
                                    <?php endif; ?>
                                    <?= Html::a('Reset', ['/company-scope'], ['class' => 'btn btn-primary']) ?>
                                </div>
                            </div>
                            <?php ActiveForm::end(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header card-header-primary">
                            <h4 class="card-title text-center text-uppercase"
                                style="font-weight: 700"><?= Html::encode($this->title) ?></h4>
                            <p class="card-category"></p>
                        </div>
                        <div class="card-body">
                            <?php if ($session['isAdmin'] == 1) { ?>
                                <p class="table-add float-right mb-3 mr-2">
                                    <?= Html::a('Create company Scope', ['create'], ['class' => 'btn btn-success']) ?>
                                </p>
                            <?php } ?>
                            <div class="table-responsive">
                                <?php Pjax::begin(); ?>
                                <?= GridView::widget([
                                    'dataProvider' => $dataProvider,
                                    'filterModel' => $searchModel,
                                    'columns' => [
                                        //  ['class' => 'yii\grid\SerialColumn'],
                                        [
                                            'class' => 'yii\grid\SerialColumn',
                                        ],
                                        'text',
                                        [
                                            'attribute' => 'isDeleted',
                                            'headerOptions' => ['style' => 'width:10%'],
                                            'filter' => Html::activeDropDownList($searchModel, 'isDeleted', ["0" => "Active", "1" => "Deactive"], ['class' => 'form-control', 'prompt' => '- Select -']),
                                            'value' => function ($model) {
                                                return Yii::$app->common->displayLabel('isDeleted', $model->isDeleted);
                                            },
                                            'contentOptions' => function ($model, $key, $index, $column) {
                                                $id = base64_encode($model->id);
                                                if ($model->isDeleted == '1') {
                                                    return ['style' => 'width:90px', 'class' => 'btn btn-danger  btn-sm activebtn', 'id' => base64_encode($model->id), 'data-toggle' => "modal", 'data-target' => "#activeModel", "onclick" => "activeDeactive('$id')"];
                                                } else {
                                                    return ['style' => 'width:90px', 'class' => 'btn btn-success  btn-sm activebtn', 'id' => base64_encode($model->id), 'data-toggle' => "modal", 'data-target' => "#deactiveModel", "onclick" => "activeDeactive('$id')"];
                                                }
                                            },
                                            'visible' => !($visible == 0),
                                        ],
                                        [
                                            'attribute' => 'isDeleted',
                                            'headerOptions' => ['style' => 'width:10%'],
                                            'filter' => Html::activeDropDownList($searchModel, 'isDeleted', ["0" => "Active", "1" => "Deactive"], ['class' => 'form-control', 'prompt' => '- Select -']),
                                            'value' => function ($model) {
                                                return Yii::$app->common->displayLabel('isDeleted', $model->isDeleted);
                                            },
                                            'visible' => $visible == 0,
                                        ],
                                        [
                                            'header' => 'Action',
                                            'class' => 'yii\grid\ActionColumn',
                                            'headerOptions' => ['style' => 'width:5%'],
                                            'template' => $action_list,
                                            'visible' => !Yii::$app->user->isGuest,
                                            'buttons' => [
                                                'view' => function ($url, $model) {
                                                    $session = Yii::$app->session;
                                                    $url = Yii::$app->urlManager->createUrl(['company-scope/view?id=' . base64_encode($model->id)]);
                                                    return Html::a('<span class="material-icons">visibility</span>', $url, [
                                                        'title' => Yii::t('app', 'View'),
                                                        'target' => '_blank',
                                                        'data-pjax' => '0',
                                                    ]);
                                                },
                                                'update' => function ($url, $model) {
                                                    $url = Yii::$app->urlManager->createUrl(['company-scope/update', 'id' => base64_encode($model->id)]);
                                                    return Html::a('<span class="material-icons">edit</span>', $url, [
                                                        'title' => Yii::t('app', 'Update'),
                                                        'data-pjax' => '0'
                                                    ]);
                                                },
                                                'delete' => function ($url, $model) {
                                                    $id = base64_encode($model->id);
                                                    return Html::a('<span class="material-icons">delete</span>', [''], ['class' => 'delete', 'id' => base64_encode($model->id), 'data-toggle' => "modal", 'data-target' => "#deleteModel", "onclick" => "deleteTerms('$id')"]);
                                                },
                                            ],
                                        ],
                                    ],
                                    'pager' => [
                                        'prevPageLabel' => '<i class="material-icons">chevron_left</i>',
                                        'nextPageLabel' => '<i class="material-icons">chevron_right</i>',
                                        // Customzing options for pager container tag
                                        'options' => [
                                            'tag' => 'ul',
                                            'class' => 'pagination',
                                            'id' => 'pager-container',
                                        ],
                                        // Customzing CSS class for pager link
                                        'linkOptions' => ['class' => 'waves-effect'],
                                        'activePageCssClass' => 'active',
                                        'disabledPageCssClass' => 'disabled',
                                        // Customzing CSS class for navigating link
                                        'prevPageCssClass' => 'mypre',
                                        'nextPageCssClass' => 'mynext',
                                    ],
                                ]);
                                Pjax::end(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
<script type="text/javascript">
    var deleteid;

    function deleteTerms($id) {
        deleteid = $id;
    }

    $('#confirm').on('click', function () {
        $.ajax({
            url: '<?php echo Yii::$app->urlManager->createUrl(['/company-scope/delete'])?>',
            type: 'POST',
            data: {id: deleteid},
            success: function (response) {
                if (response == 1) {
                    location.reload(true);
                } else {

                }

            }
        });
    });
    $("#daterange").daterangepicker({
        maxDate: new Date(),
        locale: {
            format: 'Y-MM-DD'
        }
    });
    var termid;

    function activeDeactive($id) {
        termid = $id;
    }

    $('#confirmActive').on('click', function () {
        $.ajax({
            url: '<?php echo Yii::$app->urlManager->createUrl(['/company-scope/active'])?>',
            type: 'post',
            data: {id: termid},
            success: function (data) {
                location.reload(true);
            }
        });
    });
    $('#confirmDeactive').on('click', function () {
        $.ajax({
            url: '<?php echo Yii::$app->urlManager->createUrl(['/company-scope/active'])?>',
            type: 'post',
            data: {id: termid},
            success: function (data) {
                location.reload(true);
            }
        });
    });
</script>
