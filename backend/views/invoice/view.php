<?php

use common\models\InvoiceItems;
use yii\helpers\Html;
use yii\web\YiiAsset;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\Invoice */

$this->title = $model->id;
$this->params['breadcrumbs'][] = ['label' => 'Invoices', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
YiiAsset::register($this);
?>
<style>
    .table thead th {
        font-weight: bold !important;
    }
</style>
<div class="invoice-view">
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header card-header-primary d-flex justify-content-between">
                            <?php if (!empty($model->id)) { ?>
                                <h3 class="card-title text-center text-uppercase" style="font-weight: 700">View
                                    Invoice
                                    : <?php echo $model->invoiceNumber ?></h3>
                                <p class="card-category">
                                    <label data-user="<?= Yii::$app->user->id ?>" id="invoice_id"
                                           style="display: none;"><?= $model->id; ?></label>
                                    <?= Html::Button(Yii::t('app', 'Add follow up detail'), ['class' => 'btn btn-dark', 'name' => 'btnReport', 'value' => 'showreport', 'onclick' => "addFollowUpDetail()"]) ?>
                                </p>
                            <?php } ?>
                        </div>
                        <div class="card-body">
                            <p class="table-add float-right mb-3 mr-2">
                            </p>
                            <div class="table-responsive">

                                <?= DetailView::widget([
                                    'model' => $model,
                                    'attributes' => [
                                        // 'id',
                                        'invoiceNumber',
                                        [
                                            'attribute' => 'businessId',
                                            'label' => 'Business Name',
                                            'value' => !empty(Yii::$app->common->getParentName('BUSINESS', $model->businessId)) ? Yii::$app->common->getParentName('BUSINESS', $model->businessId) : '-',
                                        ],
                                        [
                                            'attribute' => 'customerId',
                                            'label' => 'Customer Name',
                                            'value' => !empty(Yii::$app->common->getParentName('CUSTOMER', $model->customerId)) ? Yii::$app->common->getParentName('CUSTOMER', $model->customerId) : '-',
                                        ],
                                        [
                                            'attribute' => 'assignedToId',
                                            'label' => 'User Name',
                                            'value' => !empty(Yii::$app->common->getParentName('USER', $model->assignedToId)) ? Yii::$app->common->getParentName('USER', $model->assignedToId) : '-',
                                        ],
                                        'totalTaxAmount',
                                        [
                                            'attribute' => 'totalAmount',
                                            'label' => 'Invoice Value',
                                        ],
                                        'totalDiscountAmount',
                                        [
                                            'attribute' => 'pdfFile',
                                            'format' => ['raw', ['width' => '100', 'height' => '100']],
                                            'value' => function ($model) {
                                                $doc = '-';
                                                if (!empty($model->getPdfFileUrl())) {
                                                    $doc .= '<a href="' . $model->getPdfFileUrl() . '" target="_blank">  Download  </a>';
                                                    $doc = '<a href="' . $model->getPdfFileUrl() . '" target="_blank">  Download  </a>';
                                                }

                                                return $doc;
                                            },
                                        ],
                                        'status',
                                        'statusText',
                                        [
                                            'attribute' => 'invoiceDate',
                                            'label' => 'Creation Date',
                                        ],
                                        [
                                            'attribute' => 'followupDate',
                                            'label' => 'Next Followup Date',
                                        ],
                                    ],
                                ]) ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header card-header-primary d-flex justify-content-between">
                            <h3 class="card-title text-center text-uppercase" style="font-weight: 700">Invoice
                                Logs</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered">
                                    <thead>
                                    <tr>
                                        <th>Sr. No.</th>
                                        <th>Status</th>
                                        <th>Status Text</th>
                                        <th>Status Updated By</th>
                                        <th>Follow Up Date</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php /** @var InvoiceItems $item */
                                    $idx = 1;
                                    foreach ($invoiceLogs as $log): ?>
                                        <tr>
                                            <td><?= $idx++ ?></td>
                                            <td><?= $log['status'] ?></td>
                                            <td><?= $log['statusText'] ?></td>
                                            <td><?= $log['statusUpdatedByUser']['name'] ?></td>
                                            <td><?= $log['followupDate'] ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="followUpModal" tabindex="-1" role="dialog"
     aria-labelledby="followUpModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 500px !important;" role="document">
        <div class="modal-content">
            <div class="text-center mt-3"><label style="font-size: 18px;">Change Follow Up Status</label></div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <label>Status</label>
                            <select class="form-control" name="status" id="status">
                                <option value="" selected>Select Status</option>
                                <?php foreach ($statuses as $status): ?>
                                    <?php if ($status == $model->status) { ?>
                                        <option selected><?= $status ?></option>
                                    <?php } else { ?>
                                        <option><?= $status ?></option>
                                    <?php } ?>
                                <?php endforeach; ?>
                            </select>
                            <span class="text-danger" id="follow_up_status_error"></span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label>Next follow up date</label>
                            <input type="text" name="follow_up_date" id="follow_up_date"
                                   placeholder="Choose follow up date"/>
                            <span class="text-danger" id="follow_up_date_error"></span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label>Remarks</label>
                            <input type="text" name="remarks" id="remarks" placeholder="Enter follow up remark"/>
                        </div>
                    </div>
                    <div class="col-12">
                        <span class="text-danger" id="follow_up_error"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-dark" onclick="changeFollowUpDetail()">Submit</button>
            </div>
        </div>
    </div>
</div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>

<script>
    const invoice_id = $('#invoice_id').text();
    const user_id = $('#invoice_id').data('user');
    console.log(user_id);

    $("#follow_up_date").daterangepicker({
        singleDatePicker: true,
        locale: {
            format: 'Y-MM-DD'
        }
    });

    function addFollowUpDetail() {
        $('#follow_up_error').text("");
        $('#remarks').val('');
        $('#follow_up_date').val('');
        $('#followUpModal').modal('show');
    }

    function changeFollowUpDetail() {
        $('#follow_up_error').text("");
        var status = $('#status').val();
        var remarks = $('#remarks').val();
        var follow_up_date = $('#follow_up_date').val();
        var isValid = true;
        if (status === "") {
            $('#follow_up_status_error').text("Please select status");
            isValid = false;
        }
        if (follow_up_date === "") {
            $('#follow_up_date_error').text("Please select date");
            isValid = false;
        }
        if (!isValid) {
            return isValid;
        }


        $.ajax({
            type: 'POST',
            url: '/api/web/v1/invoice/update-status?userId=' + user_id,
            data: {
                'id': invoice_id,
                'status': status,
                'statusText': remarks,
                'followupDate': follow_up_date
            },
            dataType: "json",
            success: function (response) {
                if (response.success) {
                    $('#remarks').val('');
                    $('#follow_up_date').val('');
                    $('#followUpModal').modal('hide');
                    location.reload(true);
                } else {
                    $('#follow_up_error').text(response.message);
                }
            }
        });
    }
</script>
