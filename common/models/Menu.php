<?php

namespace common\models;

use common\models\enum\Key;
use common\models\enum\PlanFeature;
use rce\material\widgets\Menu as RCEmenu;
use yii\helpers\ArrayHelper;

class Menu
{
    static function getMenu()
    {

        $items = [
            ['label' => 'Dashboard', 'icon' => 'dashboard', 'url' => ['/site/index']],
//            ['label' => 'Activation', 'icon' => 'verified_user', 'url' => ['/subscription/activation']],
       //     ['label' => 'Make Quotation', 'icon' => 'picture_as_pdf', 'url' => ['/quotation/make']],
        ];
        $business = Business::getBusiness();

        // Check if business is null to avoid fatal error on hasAccess()
        if ($business === null) {
            return $items; // Return default menu without restricted items
        }

        $isFollowupEnabled = Business::getConfig(Key::GROUP_APP, Key::IS_FOLLOW_UP_ENABLED);
        $isCompanyScopeEnable = Business::getConfig(Key::GROUP_APP, Key::isCompanyScopeEnable);
        $isCustomerScopeEnable = Business::getConfig(Key::GROUP_APP, Key::isCustomerScopeEnable);
        $isBudgetTermsEnable = Business::getConfig(Key::GROUP_APP, Key::isBudgetTermsEnable);

        $isInventoryEnabled = Business::getConfig(Key::GROUP_APP, Key::IS_INVENTORY_ENABLED);
        $useSeparateInventoryItems = $isInventoryEnabled && Business::getConfig(Key::GROUP_CUSTOM, Key::useSeparateInventoryItems);

        $isQuotationEnabled = $business->hasAccess(PlanFeature::QUOTATION);
        $isBudgetEnabled = $business->hasAccess(PlanFeature::BUDGET);
        $isInvoiceEnabled = $business->hasAccess(PlanFeature::INVOICE);
        $isPoEnabled = $business->hasAccess(PlanFeature::PURCHASE_ORDER);
        $isProformaEnabled = $business->hasAccess(PlanFeature::PROFORMA_INVOICE);



        if ($isQuotationEnabled) {
            $items[] =  ['label' => 'Quotation List', 'icon' => 'picture_as_pdf', 'url' => ['/quotation/index']];
        }
        if ($isBudgetEnabled) {
            $items[] =  ['label' => 'Budget List', 'icon' => 'picture_as_pdf', 'url' => ['/budget/index']];
        }
        if ($isFollowupEnabled && 0) {
            $items[] = ['label' => 'Follow Up List', 'icon' => 'picture_as_pdf', 'url' => ['/follow-up/index']];
        }
        if ($isInvoiceEnabled) {
            $items[] = ['label' => 'Invoice List', 'icon' => 'receipt', 'url' => ['/invoice/index']];
        }
        if ($isPoEnabled) {
            $items[] = ['label' => 'PO List', 'icon' => 'list', 'url' => ['/purchase/index']];
        }
        if ($isProformaEnabled) {
            $items[] = ['label' => 'Proforma List', 'icon' => 'list', 'url' => ['/proforma/index']];
        }

        $items = ArrayHelper::merge($items, [
//            ['label' => 'Subscription', 'icon' => 'portrait', 'url' => ['/subscription/index']],
            ['label' => 'Manage Customer', 'icon' => 'portrait', 'url' => ['/customer/index']],
            ['label' => 'Manage User', 'icon' => 'group', 'url' => ['/user/index']],
            ['label' => 'Manage Category', 'icon' => 'widgets', 'url' => ['/product-category/index']],
        ]);

        if ($useSeparateInventoryItems) {
            $items[] = ['label' => 'Manage Inventory', 'icon' => 'list', 'url' => ['/inventory/index']];
        }

        $items = ArrayHelper::merge($items, [
            ['label' => 'Manage Product', 'icon' => 'store', 'url' => ['/product/index']],
            ['label' => 'My Business', 'icon' => 'business', 'url' => ['/business/index']]
        ]);


        if ($isQuotationEnabled) {
            $items[] = ['label' => 'Manage Terms Conditions', 'icon' => 'description', 'url' => ['/terms-condition/index']];
        }


        if ($isBudgetEnabled) {
            $items[] = ['label' => 'Manage Budget Terms', 'icon' => 'description', 'url' => ['/budget-terms/index']];
        }


        if ($isCompanyScopeEnable) {
            $items[] = ['label' => 'Manage Company Scope', 'icon' => 'description', 'url' => ['/company-scope/index']];
        }

        if ($isCustomerScopeEnable) {
            $items[] = ['label' => 'Manage Customer Scope', 'icon' => 'description', 'url' => ['/customer-scope/index']];
        }

        $isNotificationEnabled = Business::getConfig(Key::GROUP_APP, Key::IS_NOTIFICATION_ENABLED);

        if ($isNotificationEnabled) {
            $items[] = ['label' => 'Notification', 'icon' => 'notifications', 'url' => ['/notification/index']];
        }


        return RCEmenu::widget(
            [
                'options' => ['class' => 'nav'],
                'items' => $items,
                'itemOptions' => array('class' => 'nav-item'),
                'linkTemplate' => '<a href="{url}" class="nav-link">{icon} {label}</a>',
            ]
        );
    }

}