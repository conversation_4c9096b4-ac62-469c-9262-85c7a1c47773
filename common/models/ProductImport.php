<?php


namespace common\models;


use common\helpers\FileManager;
use common\models\enum\Key;
use ParseCsv\Csv;
use Yii;
use yii\base\Exception;
use yii\base\Model;

class ProductImport extends Model
{
    public $csvFile;
    public $categoryId;
    public $totalCount = 0, $successCount = 0, $failedCount = 0;
    public $failedProducts;
    public $errors;

    public function rules()
    {
        return [
            [['csvFile'], 'required'],
            [['csvFile'], 'file', 'extensions' => 'csv', 'checkExtensionByMimeType' => false, 'maxSize' => 1024 * 1024 * 5],
            ['categoryId', 'integer']
        ];
    }

    public function cleanData($data)
    {
        $data['price'] = str_replace(',', '', $data['price']);

        return $data;
    }

    public function upload()
    {
        FileManager::saveProductImportFile($this); // returns URL of the File
        $this->csvFile = FileManager::getFilePathFromURL($this->csvFile); // converts into file path
        $csv = new Csv();
        $csv->offset = 1;
        $business = Business::getBusiness();
        $success = $csv->parseFile($this->csvFile);
        if (!$success) {
            $this->errors[] = "CSV File Parsing issue! please check CSV file and its data format again!";
            return false;
        }
        $this->totalCount = 0;
        $this->successCount = 0;
        $this->failedCount = 0;
        foreach ($csv->data as $productData) {
            $product = null;
            $isOnlyForInventory = $productData['isOnlyForInventory'] ?? 0;
            if (!empty($productData['category'])) {
                //isOnlyForInventory
                $categoryName = $productData['category'];
                $productCategory = ProductCategory::find()->byBusiness($business->id)->byName($categoryName)->byInventoryFlag($isOnlyForInventory)->one();
                if ($productCategory == null) {
                    $productCategory = new ProductCategory();
                    $productCategory->businessId = $business->id;
                    $productCategory->name = $categoryName;
                    $productCategory->isOnlyForInventory = $isOnlyForInventory;
                    $productCategory->save();
                    $productCategory->refresh();
                }
            } else if(!empty($this->categoryId)){
                $productCategory = ProductCategory::findByPk($this->categoryId);
            }else{
                $productCategory = $business->getProductCategories()->one();
            }
            if ($productCategory == null) {
                throw new Exception("Product category is invalid!");
            }
            $categoryId = $productCategory->id;
            if (isset($productData['id'])) {
                $product = Product::findByPk($productData['id']);
            }
            if ($productData['code'] ?? false) {
                $product = $product ?? Product::findByCode($productData['code'], $business->id);
            }
            $product = $product ?? Product::findByName($productData['name'], $categoryId);
            $product = $product ?? new Product();
            $product->categoryId = $categoryId;
            if ($isOnlyForInventory) {
                $additionalFields = Business::getConfig(Key::GROUP_INVENTORY, Key::ADDITIONAL_PRODUCT_FIELDS);
            } else {
                $additionalFields = Business::getConfig(Key::GROUP_CUSTOM, Key::ADDITIONAL_PRODUCT_FIELDS);
            }
            if (!empty($additionalFields)) {
                foreach ($additionalFields as $field) {
                    $key = $field['key'];
                    if (!empty($productData[$key])){
                        $value = $productData[$key];
                        $additionalFieldsData[$key] = $value;
                    }
                }
                if (!empty($additionalFieldsData)){
                    $product->additionalFields = json_encode($additionalFieldsData, JSON_PRETTY_PRINT);
                }
            }

            $product->setAttributes($productData);
            $product->businessId = $productCategory->businessId;

            if (!empty($productData['image']) && $productData['image'] != "NULL") {
                if (\isAbsoluteUrl($productData['image'])){
                    $product->image = $productData['image'];
                }else{
                    $product->image = Yii::$app->urlManagerUpload->createAbsoluteUrl(\PRODUCT_DIR . $product->businessId . "/" . $productData['image']);
                }
            }

            if ($product->save()) {
                $this->successCount++;
            } else {
                $this->failedCount++;
                $error = $product->getErrorSummary(true)[0];
                $this->failedProducts[] = $productData['name'];
                $this->errors[] = $error;
            }
            $this->totalCount++;
        }
        FileManager::removeFileFromURL($this->csvFile); // remove file after process id done!
        return $this->successCount;
    }
}