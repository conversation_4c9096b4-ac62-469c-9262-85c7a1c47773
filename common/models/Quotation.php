<?php

namespace common\models;

use common\helpers\FileManager;
use common\models\base\QuotationBase;
use common\models\enum\DefaultBusinessSettings;
use common\models\enum\Fields;
use common\models\enum\Key;
use common\models\enum\PlanFeature;
use common\models\enum\QuotationStatus;
use common\services\EmailClient;
use common\services\PdfService;
use JsonException;
use Yii;
use yii\base\Exception;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\helpers\Url;
use yii2tech\ar\softdelete\SoftDeleteBehavior;

/**
 * This is the model class for table "quotation".
 * @property string $groupKey
 *
 * @property TermsCondition[] $terms
 * @property Attachment[] $attachments
 * @property TermsCondition[] $companyScope
 * @property TermsCondition[] $customerScope
 * @property QuotationLog[] $quotationLogs
 * @property BusinessStats $businessStats
 */
class Quotation extends QuotationBase
{

    public $DateRange;
    public $companyName;
    public $pdfFileBase64;
    public $pdfHtmlContent;
    public $pdfOutputData;
    public $pdfFile;

    public static function getStatusArray($onlyFollowupStatus = true)
    {
        $allowedFollowupStatus = Business::getConfig(Key::GROUP_APP, Key::allowedFollowupStatus);
        if (!empty($allowedFollowupStatus)) {
            return $allowedFollowupStatus;
        }
        if ($onlyFollowupStatus) {
            return QuotationStatus::getFollowupArray();
        }
        return QuotationStatus::getArray();
    }

    /**
     * @return ActiveQuery
     */
    public
    static function findActive()
    {
        return parent::find()->andWhere(['isDeleted' => 0]);
    }

    /**
     * @param $request
     * @param User $salesUser
     * @return Quotation
     * @throws Exception
     */
    public
    static function CreateOrUpdate($request, User $salesUser): Quotation
    {
        $quoteId = $request->post('id');
        $newQuotation = Quotation::findByPk($quoteId);

        if ($quoteId && $newQuotation == null) {
            throw new Exception('Invalid quotation id', 101);
        }
        $isNewRecord = false;
        $status = QuotationStatus::REVISED;

        if ($newQuotation == null) {
            $newQuotation = new Quotation();
            $isNewRecord = true;
            $status = QuotationStatus::NEW;
        }

        $isPurchaseOrder = toBool($request->post('isPurchaseOrder'));
        $isBudget = toBool($request->post('isBudget'));

        if ($isPurchaseOrder) {
            $newQuotation->templateId = $salesUser->business->getTemplateId(Key::GROUP_PURCHASE_ORDER);
        } else if ($isBudget) {
            $newQuotation->templateId = $salesUser->business->getTemplateId(Key::GROUP_BUDGET);
        } else {
            $newQuotation->isQuotation = 1;
            $newQuotation->templateId = $salesUser->business->getTemplateId(Key::GROUP_QUOTATION);
        }

        $newQuotation->setAttributes($_POST);  // load all attributes (in new User model)
        $newQuotation->assignedToId = $request->post('assignedToId', $salesUser->id);

        $quotationDate = $request->post('quotationDate');
        if ($quotationDate) {
            $newQuotation->quotationDate = date("Y-m-d", strtotime($quotationDate));
        }

        $newQuotation->status = $request->post('status', $status);
        $newQuotation->statusUpdatedById = $salesUser->id;

        if (!$newQuotation->validate()) {
            $errors = $newQuotation->getErrorSummary(true)[0];
            throw new Exception($errors, 101);
        }
        if ($newQuotation->save()) { // save new quotation
            $jsonQuotationItems = $request->post('quotationItems');
            if ($jsonQuotationItems == null && $isNewRecord) {
                throw new Exception('Please send quotation item details', 101);
            }
            $quotationItems = Json::decode($jsonQuotationItems);
            $errorMsg = $newQuotation->saveQuotationItems($quotationItems, $isNewRecord);

            if ($errorMsg) {
                throw new Exception($errorMsg, 101);
            }
        }

        if (!$newQuotation->generatePDF() && $newQuotation->getPdfFileDataUrl() == null) {
            $errorMsg = "There are issues in generating PDF File on server! Please try again later!";
            throw new Exception($errorMsg, 101);
        }

        $newQuotation->saveQuotationLogs();
        return $newQuotation;
    }

    /**
     * Finds Model by id
     *
     * @param string $id
     * @return static|null
     */
    public
    static function findByPk($id)
    {
        return static::findOne(['id' => $id]);
    }

    /**
     * @param $quotationItems
     * @param $isNewRecord
     * @return false|mixed
     */
    public
    function saveQuotationItems($quotationItems, $isNewRecord)
    {
        $updatedIds = [];
        foreach ($quotationItems as $index => $item) {
            $newItem = new QuotationItems();
            if (!empty($item['id'])) {
                $newItem->id  = $item['id'];
                $newItem->setIsNewRecord(false);
            }

            $newItem->setAttributes($item);
            $newItem->quotationId = $this->id;
            $newItem->order = $index; // Set the order based on array index
            $newItem->updatedAt = new Expression('NOW()');
            if (!$newItem->save()) {
                Yii::error(print_r($newItem->getErrors(), true));
                if ($isNewRecord) {
                    Quotation::deleteAll(['id' => $this->id]);
//                    $this->delete(); // remove quotation object - if new record and quotation item has an error!
                }
                return $newItem->getErrorSummary(true)[0];
            }
            $updatedIds[] = $newItem->id;
        }

        if (!$isNewRecord) {
            $this->removeQuotationItems($updatedIds);
        }

        return false; // no errors
    }

    public
    function removeQuotationItems($updatedIds)
    {
        // remove quotation items by id which are not available in updatedIds
        if (!empty($updatedIds)) {
            QuotationItems::deleteAll(['AND', ['quotationId' => $this->id], ['NOT IN', 'id', $updatedIds]]);
        }
    }

    public function getPdfFileDataUrl()
    {
        if ($this->pdfFileUrl) {
            return $this->pdfFileUrl;
        }
        return Url::to(['quotation/view-pdf', 'id' => $this->id]);
    }

    public
    function generatePDF($generateFileName = true)
    {
        // Use the PdfService to generate the PDF
        $pdfService = PdfService::getInstance();
        return $pdfService->generateQuotationPdf($this, $generateFileName);
    }

    /**
     * @param string $relPath
     * @return string|null
     */
    public
    function getFilePath($relPath = '')
    {
        $relativePath = sprintf(QUOTATION_DIR, $this->businessId, date('Y'), date('m')) . $relPath;
        return FileManager::getFilePath($relativePath);
    }

    public function behaviors()
    {
        return [
            'softDeleteBehavior' => [
                'class' => SoftDeleteBehavior::className(),
                'softDeleteAttributeValues' => [
                    'isDeleted' => true
                ],
            ],
        ];
    }

    public
    function rules()
    {

        return ArrayHelper::merge(parent::rules(),
            [
                ['status', 'in', 'range' => QuotationStatus::getArray()],
                [['followupDate'], 'date', 'format' => 'php:Y-m-d'],
                ['taxPercentage', 'number', 'min' => 0, 'max' => 100],
            ]);

    }

    public function getQuotationSettingsData()
    {
        return $this->getSettingsData(KEY::GROUP_QUOTATION);
    }

    public function getPoSettingsData()
    {
        return $this->getSettingsData(KEY::GROUP_PURCHASE_ORDER);
    }

    public function getBudgetSettingsData()
    {
        return $this->getSettingsData(KEY::GROUP_BUDGET);
    }

    public function getSettingsData($groupKey)
    {
        $defaultSettings = Business::getSettings($groupKey);

        if ($this->quotationSettings) {
            try {
                $quotationSettings = json_decode($this->quotationSettings, true, 512, JSON_THROW_ON_ERROR);
                return array_merge($defaultSettings, $quotationSettings);
            } catch (JsonException $e) {
                Yii::error($e);
            }
        }
        return $defaultSettings;
    }

    public function getTermsData()
    {
        if ($this->termsJson) {
            try {
                return json_decode($this->termsJson, false, 512, JSON_THROW_ON_ERROR);
            } catch (JsonException $e) {
                Yii::error($e);
            }
        }
        return $this->terms;
    }

    public function getCompanyScopeData()
    {
        if ($this->companyScopeJson) {
            try {
                return json_decode($this->companyScopeJson, false, 512, JSON_THROW_ON_ERROR);
            } catch (JsonException $e) {
                Yii::error($e);
            }
        }
        return $this->companyScope;
    }

    public function getCustomerScopeData()
    {
        if ($this->customerScopeJson) {
            try {
                return json_decode($this->customerScopeJson, false, 512, JSON_THROW_ON_ERROR);
            } catch (JsonException $e) {
                Yii::error($e);
            }
        }
        return $this->customerScope;
    }


    public function getOtherInfoData()
    {
        if ($this->otherInfo) {
            try {
                return json_decode($this->otherInfo, false, 512, JSON_THROW_ON_ERROR);
            } catch (JsonException $e) {
                Yii::error($e);
            }
        }
        return $this->otherInfo;
    }

    /**
     * @return array|ActiveRecord[]
     */
    public function getTerms()
    {
        if (empty($this->termsIds)) {
            return null;
        }
        $termIds = explode(",", $this->termsIds);
        $terms = [];
        foreach ($termIds as $termId){
            $terms[] = TermsCondition::findByPk($termId);
        }
        return $terms;
    }

    /**
     * @return array|ActiveRecord[]
     */
    public function getAttachments($type = null): ?array
    {
        if (empty($this->attachmentIds)) {
            return null;
        }
        $findQuery = Attachment::find()->where(['in', 'id', explode(",", $this->attachmentIds)]);
        if ($type != null){
            $findQuery->andWhere(['type'=>$type]);
        }
        return $findQuery->all();
    }

    public function getCompanyScope()
    {
        if (empty($this->companyScopeIds)) {
            return null;
        }
        return TermsCondition::find()->where(['in', 'id', explode(",", $this->companyScopeIds)])->all();
    }

    public function getCustomerScope()
    {
        if (empty($this->customerScopeIds)) {
            return null;
        }

        return TermsCondition::find()->where(['in', 'id', explode(",", $this->customerScopeIds)])->all();
    }

    /**
     * @return ActiveQuery
     */
    public function getQuotationLogs()
    {
        return $this->hasMany(QuotationLog::className(), ['quotationId' => 'id'])->orderBy('id desc');
    }

    public function combineAdditionalFields($config, $userInput = null)
    {
        $combinedFields = [];
        if (empty($config)) {
            return $combinedFields;
        }

        foreach ($config as $field) {
            $group = $field['group'] ?? Fields::groupInfo;
            $key = $field['key'];
            if (isset($userInput[$key])) {
                $value = isset($userInput[$key]) ? getValue($field['type'], $userInput[$key]) : '';
            } else {
                $value = getValue($field['type'], $field['value']);
            }
            // If the field is an enum and the value is not in the options, set it to the default value
            if ($field['type'] === 'enum') {
                $options = $field['options'];
                $options = str_replace(" || ", "||", $options);
                $options = explode('||', $options);
                $field['options'] = $options;
            }
            $field['value'] = $value;
            if ($field[Key::subType] ?? "" === Key::currencyTax) {
                $field[Key::amount] = !empty($value[Key::amount]) ? $value[Key::amount] : null;
                $field[Key::taxAmount] = !empty($value[Key::taxAmount]) ? $value[Key::taxAmount] : null;
            }
            $combinedFields[$group][$key] = $field;
        }
        return $combinedFields;
    }


    public function getAdditionalFieldsData()
    {
        $business = $this->business;
        $additionalFieldsConfig = $business->config(Key::GROUP_QUOTATION, Key::ADDITIONAL_FIELDS);

        if (empty($additionalFieldsConfig)) {
            return null;
        }

        $additionalFieldsData = [];
        if (!empty($this->additionalFields)) {
            $additionalFieldsData = json_decode($this->additionalFields, true);
        }

        return $this->combineAdditionalFields($additionalFieldsConfig, $additionalFieldsData);
    }


    /**
     * @return bool
     */
    public
    function beforeSoftDelete()
    {
        $this->deletedAt = new Expression('NOW()'); // log the deletion date
        return true;
    }

    public
    function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            if ($this->isPurchaseOrder) {
                $this->generatePoNumber();
            } else if ($this->isBudget) {
                $this->generateBudgetNumber();
            } else {
                $this->generateQuotationNumber();
            }
        }
        $this->saveSettings();
        if ($this->status == QuotationStatus::NEW) {
            $this->statusText = $this->assignedTo->getName();
            $this->updateUsage();
        }
        if ($this->status != QuotationStatus::NEW && $this->status != QuotationStatus::REVISED) {
            $this->saveQuotationLogs();
        }

        parent::afterSave($insert, $changedAttributes); // TODO: Change the autogenerated stub
    }

    public
    function generateQuotationNumber()
    {
        if ($this->id && empty($this->quotationNumber)) {
            $nextQuoteNumber = $this->business->quotationNumber;
            $quotationPrefix = $this->business->quotationPrefix;
            $this->quotationNumber = sprintf("%s%s", $quotationPrefix, $nextQuoteNumber);
            $this->updateAttributes(['quotationNumber']);
            $this->business->quotationNumber++; // increment new quotation number!
            $this->business->updateAttributes(['quotationNumber']);
            return true;
        }
        return false;
    }

    public
    function generateBudgetNumber()
    {
        if ($this->id && empty($this->quotationNumber)) {
            $nextQuoteNumber = $this->business->budgetNumber;
            $quotationPrefix = $this->business->budgetPrefix;
            $this->quotationNumber = sprintf("%s%s", $quotationPrefix, $nextQuoteNumber);
            $this->updateAttributes(['quotationNumber']);
            $this->business->budgetNumber++; // increment new quotation number!
            $this->business->updateAttributes(['budgetNumber']);
            return true;
        }
        return false;
    }

    public
    function generatePoNumber()
    {
        if ($this->id && empty($this->quotationNumber)) {
            $nextPoNumber = $this->business->poNumber;
            $poPrefix = $this->business->poPrefix;
            $this->quotationNumber = sprintf("%s%s", $poPrefix, $nextPoNumber);
            $this->updateAttributes(['quotationNumber']);
            $this->business->poNumber++; // increment new quotation number!
            $this->business->updateAttributes(['poNumber']);
            return true;
        }
        return false;
    }


    public
    function saveQuotationLogs()
    {
        $quotationLog = new QuotationLog();
        $quotationLog->quotationId = $this->id;
        $quotationLog->setAttributes($this->toArray());
        if (!$quotationLog->save()) {
            Yii::error(print_r($quotationLog->getErrors(), true));
        }
    }

    public
    function detail()
    {
        $findQuery = self::find();
        $findQuery->alias('q');
        $findQuery->select(['q.*', 'c.name as customer_name',
            'u.name as salesUser_name',
        ]);

        $findQuery->andWhere(['q.id' => $this->id]);
        $findQuery->joinWith('customer c');
        $findQuery->joinWith('assignedTo u');
        $findQuery->with('quotationItems')->asArray();
        return $findQuery->one();
    }


    /**
     * Sends confirmation email to user
     * @param User $user user model to with email should be send
     * @return bool whether the email was sent
     */
    public
    function sendMailToCustomer()
    {
        if ($this->getPdfFilePath() == null) {
            return false;
        }

        $composeMail = EmailClient::getInstance()->getSmtpMailer()
            ->compose(
                ['html' => 'quotation-html'],
                ['quotation' => $this, 'customer' => $this->customer]
            )
            ->attach($this->getPdfFilePath())
            ->setFrom([Yii::$app->params['noReplyEmail'] => Yii::$app->params['noReplyName']]);

        $composeMail
            ->setBcc(Yii::$app->params['supportEmail']);

        $isSent = $composeMail
            ->setTo($this->customer->email)
            ->setSubject('You have received new quotation from ' . Yii::$app->name)
            ->send();

        if ($isSent !== true) {
            Yii::error($composeMail->getErrors());
        }

        return $isSent;
    }

    /**
     * @return $path|null
     */
    public
    function getPdfFilePath()
    {
        return FileManager::getFilePathFromURL($this->pdfFileUrl) ?? null;
    }

    public function remove()
    {
        return $this->softDelete();
    }

    private function updateUsage()
    {
        $user = $this->business->owner;
        if ($this->isPurchaseOrder) {
            $user->businessStats->incrementPoCount();
            if (!$this->business->hasAccess(PlanFeature::PURCHASE_ORDER)) {
                $user->businessStats->decrementPoFreeUsageLimit();
            }
        } else if ($this->isQuotation) {
            $user->incrementQuotationCount();
            $user->businessStats->incrementQuotationCount();
            if (!$this->business->hasAccess(PlanFeature::QUOTATION)) {
                $user->decrementFreeUsageCount();
                $user->businessStats->decrementQuotationFreeUsageLimit();
            }
        } else if ($this->isBudget) {
            $user->businessStats->incrementBudgetCount();
            if (!$this->business->hasAccess(PlanFeature::BUDGET)) {
                $user->businessStats->decrementBudgetFreeUsageLimit();
            }
        }
    }

    public function getGroupKey()
    {
        if ($this->isPurchaseOrder) {
            return Key::GROUP_PURCHASE_ORDER;
        } elseif ($this->isBudget) {
            return Key::GROUP_BUDGET;
        }
        return Key::GROUP_QUOTATION;
    }


    public function saveSettings()
    {
        Business::setBusiness($this->businessId);
        $groupKey = $this->groupKey;
        $quotationSettings = Business::getSettings($groupKey);
        if ($quotationSettings) {
            try {
                $currentSettings = $this->isPurchaseOrder ? $this->getPoSettingsData() : $this->getQuotationSettingsData();
                $overwriteSettings = DefaultBusinessSettings::overwriteSettings($groupKey);
                /** @var String $settingKey */
                foreach ($overwriteSettings as $settingKey) {
                    if (isset($currentSettings[$settingKey])) {
                        $quotationSettings[$settingKey] = $currentSettings[$settingKey];
                    }
                }
                $this->quotationSettings = json_encode($quotationSettings, JSON_THROW_ON_ERROR);
            } catch (JsonException $e) {
                Yii::error($e);
            }
        }
        $this->updateAttributes(['quotationSettings']);
    }

    /**
     * @return ActiveQuery
     */
    public function getBusinessStats()
    {
        return $this->hasOne(BusinessStats::className(), ['businessId' => 'businessId']);
    }

}