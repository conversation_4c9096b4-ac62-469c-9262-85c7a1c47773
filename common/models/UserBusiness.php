<?php

namespace common\models;

use common\models\base\UserBusinessBase;
use yii\db\ActiveQuery;

/**
  * @property BusinessStats $businessStats
 */
class UserBusiness extends UserBusinessBase
{
    /**
     * @return ActiveQuery
     */
    public function getBusinessStats()
    {
        return $this->hasOne(BusinessStats::className(), ['businessId' => 'businessId']);
    }

    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);
        if ($insert){
            $this->businessStats->incrementUserCount();
        }
    }

}