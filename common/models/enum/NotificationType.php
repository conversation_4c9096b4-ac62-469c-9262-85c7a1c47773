<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 18-07-2020
 * Time: 13:59
 */

namespace common\models\enum;

use kartik\helpers\Enum;

class NotificationType extends Enum
{
    const GENERAL = "general";
    const SUBSCRIPTION = "subscription";
    const TOPIC = "topic";
    const TOPIC_ALL_USERS = "all-users";
    const TOPIC_ALL_SUB_USERS = "all-sub-users";
    const TOPIC_ALL_CUSTOMER = "all-customers";
    const NEW_INQUIRY = "new_inquiry";
    const STATUS_ASSIGNED = "assigned";
    const webUrl = "webURL";
    const whatsApp = "whatsApp";
    const WhatsApp = "WhatsApp";
    const contactUs = "contactUs";
    const STATUS_UPDATE = "status_update";


    public static function getSelectionArray(): array
    {
        $arrayItems = [];
        foreach (self::getArray() as $item) {
            $arrayItems[$item] = ucfirst($item);
        }
        return $arrayItems;
    }

    public static function getArray(): array
    {
        return [self::GENERAL, self::webUrl, self::whatsApp, self::WhatsApp, self::contactUs, self::TOPIC, self::SUBSCRIPTION];
    }

}