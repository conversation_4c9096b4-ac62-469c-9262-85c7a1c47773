<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 31-08-2020
 * Time: 04:59
 */

namespace common\models\enum;

class PlanFeature
{
    //("")
    public const QUOTATION = 'quotation';
    public const INVOICE = 'invoice';
    public const PURCHASE_ORDER = 'purchase-order';
    public const MULTI_USER = 'multi-user';
    public const PRODUCT_IMAGE = 'product-image';
    public const QUOTATION_TEMPLATE = 'quotation-template';
    public const PROFORMA_INVOICE = "proforma-invoice";
    public const BUDGET = "budget";
    public const DELIVERY_NOTE = "delivery-note";
    const RECEIPT = "receipt";

    public static function getSelectionArray(): array
    {
        $arrayItems = [];
        foreach (self::getArray() as $item) {
            $arrayItems[$item] = ucfirst($item);
        }
        return $arrayItems;
    }

    public static function getArray(): array
    {
        return [self::QUOTATION, self::INVOICE, self::PURCHASE_ORDER, self::PROFORMA_INVOICE, self::DELIVERY_NOTE, self::BUDGET, self::RECEIPT];
    }
}