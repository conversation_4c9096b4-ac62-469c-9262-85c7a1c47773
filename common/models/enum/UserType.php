<?php
/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 31-08-2020
 * Time: 04:59
 */

namespace common\models\enum;

class UserType
{
    //("")
    const USER = 'user';
    const CUSTOMER = 'customer';

    public static function getSelectionArray(): array
    {
        $arrayItems = [];
        foreach (self::getArray() as $item) {
            $arrayItems[$item] = ucfirst($item);
        }
        return $arrayItems;
    }

    public static function getArray(): array
    {
        return [self::USER, self::CUSTOMER];
    }
}