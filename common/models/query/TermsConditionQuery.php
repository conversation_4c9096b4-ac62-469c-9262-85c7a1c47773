<?php

namespace common\models\query;

use common\models\TermsCondition;
use yii\db\ActiveQuery;

/**
 * This is the ActiveQuery class for [[\common\models\TermsCondition]].
 *
 * @see \common\models\TermsCondition
 */
class TermsConditionQuery extends ActiveQuery
{
    public function byBusiness($businessId): TermsConditionQuery
    {
        $this->andWhere(['businessId' => $businessId]);
        return $this;
    }

    /**
     * @inheritdoc
     * @return TermsCondition[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * @inheritdoc
     * @return TermsCondition|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
