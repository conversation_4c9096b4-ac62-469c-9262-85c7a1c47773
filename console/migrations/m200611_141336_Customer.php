<?php
use console\migrations\BaseMigration;

/**
 * Class m200611_141336_Customer
 */
class m200611_141336_Customer extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    var $tableName = 'customer';

    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci ENGINE=InnoDB';
        }

        $this->createTable($this->tableName, [
            'id' => $this->primaryKey(),
            'businessId' => $this->integer()->notNull(),
            'email' => $this->string(191)->defaultValue(null),
            'name' => $this->string(),
            'phoneNumber' => $this->string(50),
            'addressLine1' => $this->string(),
            'addressLine2' => $this->string(),
            'addressLine3' => $this->string(),
            'profilePic' => $this->string(),

            'companyName' => $this->string(191),
            'taxNumber' => $this->string(50),
            'panNumber' => $this->string(20),

            'accessToken' => $this->string(64),
            'otpCode' => $this->integer()->unsigned(),
            'otpExpiry' => $this->timestamp(),
            'auth_key' => $this->string(32),
            'password_hash' => $this->string(),

            'status' => 'ENUM("active", "inactive", "blocked") NOT NULL DEFAULT "active"',

            'addedBy' => $this->integer(),
            'isDeleted' => $this->boolean()->defaultValue(0),
            'createdAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updatedAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
            'deletedAt' => $this->timestamp()->defaultValue(null),
        ], $tableOptions);

        //  tablename, colum-name
        list($table, $foreignKey) = ['customer', 'email'];
        $this->createIndex('idx-' . $table . '-' . $foreignKey, $table, $foreignKey);

        $this->createIndex('createdAt', $this->tableName, 'createdAt');
        $this->createIndex('updatedAt', $this->tableName, 'updatedAt');

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey, $toTableName, $toColumn) = ['customer', 'businessId', 'business', 'id'];
        $this->createIndex('idx-' . $table . '-' . $foreignKey, $table, $foreignKey);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        $this->addForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table,
            $foreignKey,
            $toTableName,
            $toColumn,
            'CASCADE',
            'CASCADE'
        );

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey, $toTableName, $toColumn) = ['customer', 'addedBy', 'user', 'id'];
        $this->createIndex('idx-' . $table . '-' . $foreignKey, $table, $foreignKey);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        $this->addForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table,
            $foreignKey,
            $toTableName,
            $toColumn,
            'CASCADE',
            'CASCADE'
        );

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey) = ['customer', 'addedBy'];
        $this->dropForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table
        );
        $this->dropIndex(
            'idx-' . $table . '-' . $foreignKey,
            $table
        );

        $this->dropTable('{{%customer}}');


        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200611_141336_Customer cannot be reverted.\n";

        return false;
    }
    */
}
