<?php
use console\migrations\BaseMigration;

/**
 * Class m200612_093753_add_inquiry_tbl
 */
class m200612_093753_add_inquiry_tbl extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            // http://stackoverflow.com/questions/766809/whats-the-difference-between-utf8-general-ci-and-utf8-unicode-ci
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci ENGINE=InnoDB';
        }

        $this->createTable('inquiry', [
            'id' => $this->primaryKey(),
            'inquiryNumber' => $this->string(25)->unique(),
            'customerId' => $this->integer()->notNull(),
            'productId' => $this->integer()->notNull(),
            'orderQuantity' => $this->integer()->notNull(),
            'isSampleRequired' => $this->boolean()->defaultValue(0),
            'assignedTo' => $this->integer(),

            'message' => $this->string(),

            'status' => 'ENUM("new", "assigned", "in-progress", "dispatched", "approved", "rejected") NOT NULL DEFAULT "new"',
            'statusText' => $this->string(),
            'statusAttachment' => $this->string(),
            'statusUpdatedBy' => $this->integer(),

            'followupDate' => $this->date(),

            'isDeleted' => $this->boolean()->defaultValue(0),
            'createdAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updatedAt' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
            'deletedAt' => $this->timestamp()->defaultValue(null),
        ], $tableOptions);


        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey, $toTableName, $toColumn) = ['inquiry', 'customerId', 'customer', 'id'];
        $this->createIndex('idx-' . $table . '-' . $foreignKey, $table, $foreignKey);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        $this->addForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table,
            $foreignKey,
            $toTableName,
            $toColumn,
            'CASCADE',
            'CASCADE'
        );

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey, $toTableName, $toColumn) = ['inquiry', 'productId', 'product', 'id'];
        $this->createIndex('idx-' . $table . '-' . $foreignKey, $table, $foreignKey);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        $this->addForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table,
            $foreignKey,
            $toTableName,
            $toColumn,
            null,
            'CASCADE'
        );

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey, $toTableName, $toColumn) = ['inquiry', 'assignedTo', 'user', 'id'];
        $this->createIndex('idx-' . $table . '-' . $foreignKey, $table, $foreignKey);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        $this->addForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table,
            $foreignKey,
            $toTableName,
            $toColumn,
            null,
            'CASCADE'
        );



        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey, $toTableName, $toColumn) = ['inquiry', 'statusUpdatedBy', 'user', 'id'];
        $this->createIndex('idx-' . $table . '-' . $foreignKey, $table, $foreignKey);

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        $this->addForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table,
            $foreignKey,
            $toTableName,
            $toColumn,
            null,
            'CASCADE'
        );

        $this->createIndex('createdAt', 'inquiry', 'createdAt');
        $this->createIndex('updatedAt', 'inquiry', 'updatedAt');

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey) = ['inquiry', 'customerId'];
        $this->dropForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table
        );
        $this->dropIndex(
            'idx-' . $table . '-' . $foreignKey,
            $table
        );

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey) = ['inquiry', 'productId'];
        $this->dropForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table
        );
        $this->dropIndex(
            'idx-' . $table . '-' . $foreignKey,
            $table
        );

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey) = ['inquiry', 'assignedTo'];
        $this->dropForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table
        );
        $this->dropIndex(
            'idx-' . $table . '-' . $foreignKey,
            $table
        );

        //  foreignkey-name, tablename, colum-name, to-tablename, to-column
        list($table, $foreignKey) = ['inquiry', 'statusUpdatedBy'];
        $this->dropForeignKey(
            'fk-' . $table . '-' . $foreignKey,
            $table
        );
        $this->dropIndex(
            'idx-' . $table . '-' . $foreignKey,
            $table
        );


        $this->dropTable($table); // Drop Inquiry table


    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m200612_093753_add_inquiry_tbl cannot be reverted.\n";

        return false;
    }
    */
}
