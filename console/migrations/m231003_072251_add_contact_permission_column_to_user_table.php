<?php

use yii\db\Migration;

/**
 * <PERSON>les adding columns to table `{{%user}}`.
 */
class m231003_072251_add_contact_permission_column_to_user_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%user}}', 'isContactsAllowed', $this->boolean()->defaultValue(0)->after('isPhoneVerified'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%user}}', 'isContactsAllowed');
    }
}
