<?php

use console\migrations\BaseMigration;


/**
 * Class m231222_113130_add_proforma_invoice_fields_to_business_stats_table
 */
class m231222_113130_add_proforma_invoice_fields_to_business_stats_table extends BaseMigration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%business_stats}}', 'piFreeUsageLimit',  $this->integer()->unsigned()->defaultValue(0)->after('poFreeUsageLimit'));
        $this->addColumn('{{%business_stats}}', 'piCount', $this->integer()->unsigned()->defaultValue(0)->after('poCount'));

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%business_stats}}', 'piFreeUsageLimit');
        $this->dropColumn('{{%business_stats}}', 'piCount');
    }
}
