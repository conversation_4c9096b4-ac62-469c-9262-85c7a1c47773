# Quotation Pro App - API Configuration Guide

This document provides a comprehensive guide to the configuration system used in the Quotation Pro App API, including environment setup, parameter management, and best practices for extending the configuration.

## Table of Contents

1. [Configuration Architecture](#configuration-architecture)
2. [Environment Configuration](#environment-configuration)
3. [Application Settings](#application-settings)
4. [API-Specific Configuration](#api-specific-configuration)
5. [Multi-Tenant Configuration](#multi-tenant-configuration)
6. [Configuration Best Practices](#configuration-best-practices)
7. [Extending the Configuration](#extending-the-configuration)
8. [Troubleshooting](#troubleshooting)

## Configuration Architecture

The Quotation Pro App uses a layered configuration system that allows for environment-specific settings and application-level overrides.

### Configuration Layers

1. **Base Configuration**: Core settings defined in `common/config/main.php` and `common/config/params.php`
2. **Environment Configuration**: Environment-specific settings in `common/config/apps-settings/{APP_ID}/main.php` and `params.php`
3. **Application Settings**: Dynamic settings stored in the database and cached, with defaults in `common/config/app-settings-params.php`
4. **Local Overrides**: Developer-specific settings in `*-local.php` files (not committed to version control)

### Configuration Loading Process

```
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│ common/config/      │     │ common/config/      │     │ common/config/      │
│ main.php            │ ──► │ main-local.php      │ ──► │ apps-settings/      │
│ (Base configuration)│     │ (Local overrides)   │     │ {APP_ID}/main.php   │
└─────────────────────┘     └─────────────────────┘     │ (Environment config) │
                                                        └─────────────────────┘
                                                                   │
                                                                   ▼
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│ common/config/      │     │ common/config/      │     │ common/config/      │
│ params.php          │ ──► │ params-local.php    │ ──► │ apps-settings/      │
│ (Global parameters) │     │ (Local parameters)  │     │ {APP_ID}/params.php │
└─────────────────────┘     └─────────────────────┘     │ (Environment params)│
                                                        └─────────────────────┘
                                                                   │
                                                                   ▼
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│ common/config/      │     │ common/config/      │     │ Database-stored     │
│ app-settings-       │ ──► │ apps-settings/      │ ──► │ settings            │
│ params.php          │     │ {APP_ID}/app-       │     │ (Runtime overrides) │
│ (Default settings)  │     │ settings-params.php │     └─────────────────────┘
└─────────────────────┘     │ (Environment        │
                           │ settings)           │
                           └─────────────────────┘
```

## Environment Configuration

The application uses the `APP_ID` parameter to determine which environment configuration to load.

### Setting the APP_ID

The `APP_ID` is defined in `common/config/main-local.php`:

```php
// Define the application ID
define('APP_ID', 'quotation-dev');

// Define the application name (optional)
define('APP_NAME', 'Quotation Maker');
```

### Available Environments

| APP_ID | Description | Use Case |
|--------|-------------|----------|
| `quotation-local` | Local development | Development on local machine |
| `quotation-dev` | Development server | Testing on development server |
| `quotation-prod` | Production | Live production environment |
| `quotation-premium` | Premium version | Premium version with enhanced features |
| `quotation-custom-dev` | Custom development | Custom client development |
| `quotation-custom-local` | Custom local | Custom client local development |
| `estimate-prod` | Estimate Maker | Estimate Maker variant (production) |

### Environment-Specific Configuration Files

Each environment has its own set of configuration files in `common/config/apps-settings/{APP_ID}/`:

1. **main.php**: Database connection and component configurations
   ```php
   return [
       'name' => APP_NAME,
       'id' => APP_ID,
       'components' => [
           'db' => [
               'class' => 'yii\db\Connection',
               'dsn' => 'mysql:host=127.0.0.1;dbname=praxinfo_quotation-dev',
               'username' => 'praxinfo_quotation-dev',
               'password' => 'dev@quotation9',
               'charset' => 'utf8mb4',
           ],
           'mailer' => [
               'class' => \yii\symfonymailer\Mailer::class,
               'transport' => [
                   'dsn' => 'native://default',
               ],
               'viewPath' => '@common/mail',
               'useFileTransport' => false,
           ],
       ],
   ];
   ```

2. **params.php**: Environment-specific parameters
   ```php
   return array_merge($appSettings, [
       'adminEmail' => ['<EMAIL>'],
       'adminName' =>  APP_NAME . ' - Admin',
       'supportEmail' => '<EMAIL>',
       'supportName' => "DEV - " . APP_NAME . ' - Support',
       // Other environment-specific parameters
   ]);
   ```

3. **app-settings-params.php**: Application settings overrides
   ```php
   return [
       Key::FREE_USAGE_LIMIT =>  "10",
       Key::QUOTATION_FREE_USAGE_LIMIT =>  "20",
       Key::PURCHASE_ORDER_FREE_USAGE_LIMIT =>  "10",
       // Other application settings
   ];
   ```

## Application Settings

The application uses a dynamic settings system that allows for runtime configuration changes without code deployment.

### Settings Storage

Application settings are stored in the `app_settings` table with the following structure:

| Column | Description |
|--------|-------------|
| `id` | Primary key |
| `type` | Data type (string, int, boolean) |
| `key` | Setting key |
| `value` | Setting value |
| `description` | Setting description |

### Default Settings

Default settings are defined in `common/config/app-settings-params.php` and can be overridden by environment-specific settings in `common/config/apps-settings/{APP_ID}/app-settings-params.php`.

```php
return [
    Key::CONTACT_NUMBER =>  "+919510962986",
    Key::SECONDARY_CONTACT_NUMBER =>  "+919328363831",
    Key::CONTACT_EMAIL =>  "<EMAIL>",
    Key::WEBSITE =>  "https://www.praxinfo.com",
    // Other default settings
];
```

### Accessing Settings

Settings can be accessed through the `getParam()` function:

```php
$contactNumber = getParam(Key::CONTACT_NUMBER);
```

Or through the `Business::config()` method for business-specific settings:

```php
$taxSettings = $business->config(Key::GROUP_QUOTATION, Key::TAX_SETTINGS);
```

### Synchronizing Settings

Application settings can be synchronized with the database using the console command:

```bash
./yii batch-update/sync-app-settings
```

## API-Specific Configuration

The API module has its own configuration files in `api/config/`:

### API Main Configuration

The `api/config/main.php` file contains API-specific configuration:

```php
return [
    'id' => 'app-api',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'modules' => [
        'v1' => [
            'basePath' => '@app/modules/v1',
            'class' => 'api\modules\v1\Module'
        ]
    ],
    'components' => [
        'request' => [
            'cookieValidationKey' => '-DMwy3hdI3h9ZNa-f9zOY4YeE9javnAv',
            'parsers' => [
                'application/json' => JsonParser::class,
            ]
        ],
        'user' => [
            'identityClass' => User::class,
            'enableAutoLogin' => false,
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'flushInterval' => 1,
            'targets' => [
                [
                    'class' => FileTarget::class,
                    'exportInterval' => 1,
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [
                'v1/<controller:\w+>/<action:\w+>' => 'v1/<controller>/<action>',
                'v1/<controller:\w+>/<action:\w+>/<function:\w+>' => 'v1/<controller>/<action>'
            ],
        ]
    ],
    'params' => $params,
];
```

### API Parameters

API-specific parameters can be defined in `api/config/params.php` and `api/config/params-local.php`. The default `api/config/params.php` file is empty, allowing for all parameters to be inherited from the common configuration:

```php
<?php
return [];
```

### API Bootstrap Process

The API application is bootstrapped in `api/web/index.php`:

```php
<?php
defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

require(__DIR__ . '/../../vendor/autoload.php');
require(__DIR__ . '/../../vendor/yiisoft/yii2/Yii.php');
require(__DIR__ . '/../../common/config/bootstrap.php');

$config = yii\helpers\ArrayHelper::merge(
    require(__DIR__ . '/../../common/config/main.php'),
    require(__DIR__ . '/../../common/config/main-local.php'),
    require(__DIR__ . '/../config/main.php'),
    require(__DIR__ . '/../config/main-local.php')
);

$application = new yii\web\Application($config);
$application->run();
```

This bootstrap process merges the following configuration files in order:

1. `common/config/main.php` - Common base configuration
2. `common/config/main-local.php` - Environment-specific common configuration
3. `api/config/main.php` - API-specific configuration
4. `api/config/main-local.php` - Environment-specific API configuration

The `common/config/bootstrap.php` file sets up important path aliases used throughout the application:

```php
<?php
Yii::setAlias('@common', dirname(__DIR__));
Yii::setAlias('@frontend', dirname(dirname(__DIR__)) . '/frontend');
Yii::setAlias('@backend', dirname(dirname(__DIR__)) . '/backend');
Yii::setAlias('@templates', dirname(dirname(__DIR__)) . '/templates');
Yii::setAlias('@api', dirname(dirname(__DIR__)) . '/api');

Yii::setAlias('@console', dirname(dirname(__DIR__)) . '/console');
Yii::setAlias('@root', dirname(dirname(__DIR__)));

// Path settings for uploads
Yii::setAlias('@uploads', dirname(dirname(__DIR__)) . '/uploads/');
Yii::setAlias('@imagesDir', Yii::getAlias('@uploads') . 'images/');
Yii::setAlias('@userDir', Yii::getAlias('@uploads') . 'user/');
Yii::setAlias('@customerDir', Yii::getAlias('@uploads') . 'customer/');
Yii::setAlias('@categoryDir', Yii::getAlias('@uploads') . 'category/');
Yii::setAlias('@productDir', Yii::getAlias('@uploads') . 'product/');
Yii::setAlias('@inquiryDir', Yii::getAlias('@uploads') . 'inquiry/');
Yii::setAlias('@quotationDir', Yii::getAlias('@uploads') . 'quotation/');

Yii::setAlias('@appConfig', Yii::getAlias('@common') . '/config');

require_once(Yii::getAlias('@common/helpers/functions.php'));
```

## Multi-Tenant Configuration

The application supports multi-tenant configuration through business-specific settings.

### Business Domains

Custom domains for businesses are configured in `common/config/business-domains.php`:

```php
return [
    'custom.quotationmaker.app' => 'praxinfo',
    'premium.quotationmaker.app' => 'praxinfo',
    'custom2.quotationmaker.app' => 'praxinfotech',
    'tubefitting.quotationmaker.app' => 'tubefitting',
    // Other domain mappings
];
```

### Business Settings

Business-specific settings are stored in the `business_settings` table and can be accessed through the `Business::config()` method:

```php
$taxSettings = $business->config(Key::GROUP_QUOTATION, Key::TAX_SETTINGS);
```

## Configuration Best Practices

### Security Considerations

1. **Sensitive Information**: Never store sensitive information like API keys, passwords, or tokens in version-controlled configuration files. Use environment variables or `*-local.php` files instead.

2. **Environment Variables**: Use environment variables for sensitive information:
   ```php
   'components' => [
       'stripe' => [
           'apiKey' => env('STRIPE_API_KEY'),
           'secretKey' => env('STRIPE_SECRET_KEY'),
       ],
   ],
   ```

3. **Local Configuration**: Keep environment-specific settings in `*-local.php` files that are not committed to version control.

### Performance Optimization

1. **Caching**: Use caching for frequently accessed settings:
   ```php
   $cachedSetting = Yii::$app->cache->getOrSet('setting_key', function() {
       return AppSettings::getSetting('setting_key');
   }, 3600); // Cache for 1 hour
   ```

2. **Lazy Loading**: Use lazy loading for components that are not always needed:
   ```php
   'components' => [
       'mailer' => [
           'class' => 'yii\swiftmailer\Mailer',
           'lazyLoad' => true,
       ],
   ],
   ```

## Extending the Configuration

### Adding New Settings

To add a new application setting:

1. Define the setting key in `common/models/enum/Key.php`:
   ```php
   const NEW_SETTING_KEY = 'new_setting_key';
   ```

2. Add the default value in `common/config/app-settings-params.php`:
   ```php
   Key::NEW_SETTING_KEY => 'default_value',
   ```

3. Add the setting to the `AppSettings::getDefaults()` method:
   ```php
   ['string', Key::NEW_SETTING_KEY, env(Key::NEW_SETTING_KEY), "Description of the setting"],
   ```

4. Synchronize the settings with the database:
   ```bash
   ./yii batch-update/sync-app-settings
   ```

### Adding New Environment

To add a new environment configuration:

1. Create a new directory in `common/config/apps-settings/`:
   ```bash
   mkdir common/config/apps-settings/new-environment
   ```

2. Create the required configuration files:
   ```bash
   touch common/config/apps-settings/new-environment/main.php
   touch common/config/apps-settings/new-environment/params.php
   touch common/config/apps-settings/new-environment/app-settings-params.php
   ```

3. Configure the new environment in the files created above.

## Troubleshooting

### Common Configuration Issues

1. **Missing APP_ID**: If you see the error "Please setup APP_ID in main-local configuration file!!", make sure to define the APP_ID in `common/config/main-local.php`.

2. **Database Connection Errors**: Check the database configuration in `common/config/apps-settings/{APP_ID}/main.php`.

3. **Missing Parameters**: If a parameter is not found, check the loading order:
   - `common/config/params.php`
   - `common/config/params-local.php`
   - `common/config/apps-settings/{APP_ID}/params.php`

### Debugging Configuration

To debug configuration issues:

1. **Print Configuration**: Use `var_dump()` or `print_r()` to print the configuration:
   ```php
   echo '<pre>';
   print_r(Yii::$app->params);
   echo '</pre>';
   exit;
   ```

2. **Check Environment**: Verify the current environment:
   ```php
   echo 'APP_ID: ' . (defined('APP_ID') ? APP_ID : 'Not defined');
   ```

3. **Check Component Configuration**: Verify component configuration:
   ```php
   echo '<pre>';
   print_r(Yii::$app->getComponents());
   echo '</pre>';
   exit;
   ```
