# CORS Configuration Guide

## Overview

This document explains the Cross-Origin Resource Sharing (CORS) configuration for the Quotation Pro API and how to troubleshoot CORS-related issues.

## What Was Fixed

### Issues Identified

1. **🚨 CRITICAL: HTTP Method Blocking**: Root `.htaccess` was blocking PUT, PATCH, DELETE methods that CORS advertised as allowed
2. **Conflicting CORS Headers**: Both `api/web/.htaccess` and the Yii2 application were setting CORS headers, causing conflicts
3. **Invalid CORS Configuration**: Using wildcard `'*'` for origins while setting `Access-Control-Allow-Credentials: true` violates the CORS specification
4. **Header Duplication**: Multiple sources setting the same headers could cause browsers to reject requests

### Solutions Implemented

1. **🔧 CRITICAL FIX: Updated root `.htaccess`**: Aligned Apache HTTP method restrictions with CORS configuration
2. **Removed CORS handling from `api/web/.htaccess`**: Let Yii2 handle CORS exclusively at the application level
3. **Configured wildcard origin with credentials disabled**: Using `'*'` for origins with `Access-Control-Allow-Credentials: false` for maximum compatibility
4. **Enhanced header configuration**: Added missing headers and improved the overall CORS setup

## Current CORS Configuration

### Allowed Origins

The API currently allows requests from **any origin** using the wildcard `'*'` configuration. This provides maximum compatibility for cross-origin requests from any domain.

### Allowed Methods

- GET
- POST
- PUT
- PATCH
- DELETE
- HEAD
- OPTIONS

### Allowed Headers

- Content-Type
- Authorization
- AUTH_TOKEN (Custom authentication header)
- X-Requested-With
- Accept
- Origin
- Cache-Control
- X-File-Name
- X-HTTP-Method-Override
- UUID (For device identification)

### Exposed Headers

- X-Pagination-Per-Page
- X-Pagination-Total-Count
- X-Pagination-Current-Page
- X-Pagination-Page-Count
- X-Rate-Limit-Limit
- X-Rate-Limit-Remaining
- X-Rate-Limit-Reset
- AUTH_TOKEN
- Content-Disposition

### Other Settings

- **Credentials**: Disabled (`Access-Control-Allow-Credentials: false`) - Required when using wildcard origin
- **Max Age**: 86400 seconds (24 hours)

## Testing CORS Configuration

### Using the Test Page

1. Open `api/web/cors-test.html` in a web browser
2. Serve it from a different origin than your API (different port/domain)
3. Run the various tests to verify CORS functionality

### Manual Testing with Browser DevTools

1. Open browser DevTools (F12)
2. Go to Network tab
3. Make a request to the API from a different origin
4. Check the response headers for CORS headers

### Expected Headers in Response

For a successful CORS request, you should see these headers in the response:

```
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: false
Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE, HEAD, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization, AUTH_TOKEN, ...
Access-Control-Max-Age: 86400
```

## Switching to Specific Origins (If Needed)

The current configuration uses wildcard `'*'` which allows any origin. If you need to restrict to specific origins and enable credentials, you can modify the configuration:

1. Edit `api/modules/v1/controllers/BaseApiController.php`
2. Replace the wildcard with specific origins and enable credentials:

```php
'Origin' => [
    'https://your-domain.com',
    'https://app.your-domain.com',
    'http://localhost:3000', // for development
],
'Access-Control-Allow-Credentials' => true, // Enable credentials
```

## Troubleshooting Common CORS Issues

### 1. "CORS policy: No 'Access-Control-Allow-Origin' header"

**Cause**: The request origin is not in the allowed origins list.

**Solution**: Add the origin to the `Origin` array in `BaseApiController.php`.

### 2. "CORS policy: Credentials mode 'include' not supported with wildcard origin"

**Cause**: Trying to use credentials with wildcard origin.

**Solution**: Either use `credentials: 'omit'` in your requests, or switch to specific origins with credentials enabled.

### 3. "CORS policy: Request header 'custom-header' is not allowed"

**Cause**: Custom header not in the allowed headers list.

**Solution**: Add the header to the `Access-Control-Allow-Headers` array.

### 4. Preflight OPTIONS request failing

**Cause**: Server not properly handling OPTIONS requests.

**Solution**: The Yii2 CORS filter automatically handles this. Ensure the filter is properly configured.

## Security Considerations

1. **Wildcard with credentials disabled**: Current configuration allows any origin but prevents credential-based attacks
2. **No sensitive data in public APIs**: Since any origin can access the API, avoid exposing sensitive data without proper authentication
3. **Consider rate limiting**: Implement rate limiting to prevent abuse from any origin
4. **Use HTTPS in production**: Always use secure connections in production
5. **Monitor API usage**: Keep track of API usage patterns to detect potential abuse

## Environment-Specific Configuration

Consider creating environment-specific origin lists:

```php
$allowedOrigins = [];

if (YII_ENV_DEV) {
    $allowedOrigins = array_merge($allowedOrigins, [
        'http://localhost:3000',
        'http://localhost:8080',
        // ... other dev origins
    ]);
}

if (YII_ENV_PROD) {
    $allowedOrigins = array_merge($allowedOrigins, [
        'https://quotationmaker.app',
        'https://www.quotationmaker.app',
        // ... other prod origins
    ]);
}
```

## Files Modified

1. **`.htaccess`** (ROOT): **CRITICAL FIX** - Updated HTTP method restrictions to match CORS configuration
2. **`api/web/.htaccess`**: Removed conflicting CORS headers
3. **`api/modules/v1/controllers/BaseApiController.php`**: Fixed CORS configuration
4. **`api/web/cors-test.html`**: Added test page (new file)
5. **`tests/cors-method-test.js`**: Added HTTP method testing script (new file)
6. **`docs/CORS_CONFIGURATION.md`**: This documentation (new file)

## Further Reading

- [MDN CORS Documentation](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)
- [Yii2 CORS Filter Documentation](https://www.yiiframework.com/doc/api/2.0/yii-filters-cors)
