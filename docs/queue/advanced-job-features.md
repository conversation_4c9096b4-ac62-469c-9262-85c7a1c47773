# Advanced Job Features

This document describes advanced features of the queue system, particularly focusing on the Job and CallableJob classes.

## Table of Contents

1. [Queue-Aware Jobs](#queue-aware-jobs)
2. [Callable Jobs](#callable-jobs)
3. [Synchronous vs. Asynchronous Execution](#synchronous-vs-asynchronous-execution)
4. [<PERSON>rro<PERSON> Handling](#error-handling)
5. [Best Practices](#best-practices)

## Queue-Aware Jobs

Jobs can be "queue-aware," meaning they can access the queue instance that is processing them. This allows for more complex job implementations that need to interact with the queue.

### Using the Queue in Job Handlers

All job classes that extend the base `Job` class can access the queue instance in their `handle` method:

```php
namespace common\jobs;

use yii\queue\Queue;

class MyComplexJob extends Job
{
    public $data;

    /**
     * Handle the job with access to the queue
     *
     * @param Queue|null $queue The queue instance (provided automatically)
     * @return mixed
     */
    public function handle($queue = null)
    {
        // Process the job
        $result = $this->processData($this->data);

        // If we have a queue instance, we can push additional jobs
        if ($queue !== null) {
            $queue->push(new AnotherJob(['data' => $result]));
        }

        return true;
    }
}
```

### Queue Parameter is Optional

The queue parameter in the `handle` method is optional. This allows your jobs to work in both queue and synchronous execution modes:

```php
public function handle($queue = null)
{
    // Check if we have a queue instance
    if ($queue !== null) {
        // Do queue-specific operations
    } else {
        // Do synchronous operations
    }

    // Common operations
    return $this->processData();
}
```

### How It Works

When a job is executed by a queue worker:

1. The queue system deserializes the job from storage
2. The queue system calls the job's `execute($queue)` method, passing the queue instance
3. Our `execute` method uses reflection to check if the `handle` method expects a queue parameter
4. If it does, the queue instance is passed to the `handle` method
5. If not, `handle` is called without parameters

## Callable Jobs

The system supports using PHP callables (closures, functions, etc.) as jobs through the `CallableJob` class.

### Basic Usage

```php
// Using QueueService
$queueService = QueueService::getInstance();
$queueService->push(function($data) {
    // Process data
    return true;
}, ['key' => 'value']);

// Using the queue component directly
Yii::$app->queue->push(new CallableJob([
    'callable' => function($data) {
        // Process data
        return true;
    },
    'data' => ['key' => 'value']
]));
```

### Queue-Aware Callables

Callables can also be queue-aware by accepting a second parameter:

```php
$queueService->push(function($data, $queue) {
    // Process data
    $result = processData($data);

    // Push another job to the queue
    $queue->push(new AnotherJob(['data' => $result]));

    return true;
}, ['key' => 'value']);
```

### How It Works

The `CallableJob` class uses reflection to determine how many parameters the callable expects:

1. If the callable expects more than one parameter, both the data and the queue instance are passed
2. Otherwise, only the data is passed

This allows for flexible callable jobs that can optionally interact with the queue.

## Synchronous vs. Asynchronous Execution

The system supports both synchronous and asynchronous execution of jobs.

### Asynchronous Execution

When a job is pushed to the queue, it will be executed asynchronously by a queue worker:

```php
// Push a job to the queue for asynchronous execution
$queueService->push(new MyJob(['data' => $data]));
```

### Synchronous Execution

Jobs can also be executed synchronously using the `sync` method:

```php
// Execute a job synchronously
$queueService->sync(new MyJob(['data' => $data]));
```

### Fallback to Synchronous Execution

If the queue is not available (e.g., Redis is down), the system will automatically fall back to synchronous execution:

```php
// This will use the queue if available, or execute synchronously if not
$emailService->queue()->sendUserMail($user, $template, $params, $subject);
```

### How It Works

The system uses the `executeSync` method for synchronous execution:

1. For jobs that extend the base `Job` class, `executeSync` calls the `handle` method
2. For callable jobs, `executeSync` calls the callable with just the data parameter
3. Error handling is different in synchronous mode - exceptions are caught and logged, but not thrown

## Error Handling

The system has different error handling strategies for queue and synchronous execution modes.

### Queue Mode Error Handling

In queue mode, exceptions are re-thrown to allow the queue to handle retries:

```php
try {
    return $this->handle($queue);
} catch (\Exception $e) {
    // Log the error
    Yii::error("Job failed: " . get_class($this) . ". Error: " . $e->getMessage());

    // Re-throw the exception for the queue to handle
    throw $e;
}
```

### Synchronous Mode Error Handling

In synchronous mode, exceptions are caught, logged, and a failure result is returned:

```php
try {
    return $this->handle();
} catch (\Exception $e) {
    // Log the error
    Yii::error("Job failed in sync mode: " . get_class($this) . ". Error: " . $e->getMessage());

    // Return false instead of throwing the exception
    return false;
}
```

This prevents exceptions from disrupting the main process flow when executing jobs synchronously.

## Generic Email Job

The `GenericEmailJob` provides a flexible way to send any type of email asynchronously using the queue system. It directly calls the `sendMail` method of the `EmailService` class.

### Basic Usage

```php
$queueService->push(new \common\jobs\GenericEmailJob([
    'to' => '<EMAIL>',
    'subject' => 'Important Notification',
    'view' => ['html' => 'notification-template'],
    'params' => [
        'userName' => 'John Doe',
        'message' => 'This is an important notification.',
    ],
]));
```

### Advanced Usage

```php
$queueService->push(new \common\jobs\GenericEmailJob([
    'to' => $user->email,
    'subject' => 'Your Account Summary',
    'view' => ['html' => 'account-summary', 'text' => 'account-summary-text'],
    'params' => [
        'user' => $user,
        'stats' => $userStats,
        'recentActivity' => $recentActivity,
    ],
    'options' => [
        'cc' => '<EMAIL>',
        'bcc' => ['<EMAIL>', '<EMAIL>'],
        'from' => ['<EMAIL>' => 'Example Company'],
        'replyTo' => '<EMAIL>',
        'attachments' => [
            '/path/to/summary.pdf',
            ['path' => '/path/to/terms.pdf', 'options' => ['as' => 'Terms_and_Conditions.pdf']],
        ],
    ],
    'useSmtp' => true,
]));
```

### Configuration Options

- `to`: The recipient email address(es)
- `subject`: The email subject
- `view`: The view template to use (string or array with 'html' and 'text' keys)
- `params`: The parameters to pass to the view
- `options`: Additional options
  - `cc`: The CC recipient(s)
  - `bcc`: The BCC recipient(s)
  - `from`: The sender email address and name
  - `replyTo`: The reply-to email address
  - `attachments`: The email attachments
- `useSmtp`: Whether to use SMTP for sending the email

## Best Practices

### 1. Make Jobs Queue-Agnostic When Possible

Design your jobs to work without requiring a queue instance when possible:

```php
public function handle($queue = null)
{
    // Focus on the core job logic that doesn't require a queue
    return $this->processData();
}
```

### 2. Use Queue Parameter for Advanced Features Only

Only use the queue parameter when you need advanced features like pushing additional jobs:

```php
public function handle($queue = null)
{
    $result = $this->processData();

    // Only use the queue for advanced features
    if ($queue !== null && $this->shouldCreateFollowUpJob($result)) {
        $queue->push(new FollowUpJob(['data' => $result]));
    }

    return true;
}
```

### 3. Handle Both Execution Modes Gracefully

Ensure your jobs work correctly in both queue and synchronous execution modes:

```php
public function handle($queue = null)
{
    // Core logic works in both modes
    $result = $this->processData();

    // Queue-specific operations are conditional
    if ($queue !== null) {
        // Queue-specific operations
    } else {
        // Alternative approach for synchronous execution
    }

    return true;
}
```

### 4. Use Callable Jobs for Simple Tasks

Use callable jobs for simple, self-contained tasks:

```php
$queueService->push(function($data) {
    // Simple, self-contained task
    return sendNotification($data['userId'], $data['message']);
}, ['userId' => $user->id, 'message' => 'Hello!']);
```

### 5. Use Job Classes for Complex Tasks

Use job classes for complex tasks that require more structure:

```php
$queueService->push(new ProcessOrderJob([
    'orderId' => $order->id,
    'sendNotification' => true,
    'generateInvoice' => true,
]));
```

By following these best practices, you can create a robust and flexible queue system that handles both simple and complex tasks efficiently.
