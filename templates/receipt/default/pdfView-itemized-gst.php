<?php

/**
 * Created by PhpStorm.
 * User: Umang-PC
 * Date: 06-07-2020
 * Time: 09:56
 */

/* @var $this yii\web\View */
/* @var $template common\models\Template */
/* @var $customSettings [] */
/* @var $appSettings [] */
/* @var $invoice common\models\Invoice */
/* @var $business common\models\Business */
/* @var $invoiceItems common\models\InvoiceItems[] */
/* @var $terms common\models\TermsCondition[] */
/* @var $otherInfo common\models\enum\OtherInfo */
/* @var $shouldDisplayWatermark boolean  */
/* @var $watermarkLink string  */
/* @var $currentUser common\models\User */


/* @var $assetsUrl string */

use common\models\Business;
use common\models\enum\DiscountType;
use common\models\enum\Key;
use yii\i18n\Formatter;


$this->title = $invoice->id;
Business::setBusiness($invoice->businessId);
// Fetch settings
$invoiceSettings = $invoice->getInvoiceSettingsData();
// fetch and set vars for Invoice Settings

$discountSettings = $invoiceSettings[Key::DISCOUNT_SETTINGS]; // "no_discount", "no_discount,per_item,on_total"
$taxRateOnTotal = $invoiceSettings[Key::TAX_RATE_ON_TOTAL];
$taxSettings = $invoiceSettings[Key::TAX_SETTINGS]; // "no_tax", "no_tax,per_item,on_total"
$topMessage = $invoiceSettings[Key::TOP_MESSAGE];
$bottomMessage = $invoiceSettings[Key::BOTTOM_MESSAGE];
$isDisplayBankDetails = $invoiceSettings[Key::IS_DISPLAY_BANK_DETAILS];
$isDisplayHsnCode = $invoiceSettings[Key::IS_DISPLAY_HSN_CODE] ?? 1;
$isPagebreakBeforeTerms = $invoiceSettings[Key::IS_PAGE_BREAK_BEFORE_TERMS] ?? 0;
$isDisplaySignatureBlock = $receiptSettings[Key::isDisplaySignatureBlock] ?? 1;

// Multi-user settings
$multiUserSettings = $business->settings(Key::GROUP_MULTI_USER);
$allowUserSignature = $multiUserSettings[Key::allowUserSignature] ?? 0;

// Determine which signature to use
$signatureImg = null;
if ($allowUserSignature && isset($currentUser) && !empty($currentUser->signatureImg)) {
    $signatureImg = $currentUser->signatureImg;
} elseif (!empty($business->signatureImg)) {
    $signatureImg = $business->signatureImg;
}

// settings end

/** @var Formatter $formatter */
$formatter = Yii::$app->formatter;
$formatter->locale = $appSettings[Key::LOCALE_CODE];
$formatter->dateFormat = $appSettings[Key::DATE_FORMAT];
$business = $invoice->business;
$businessUser = $business->owner;
$customer = $invoice->customer;

// assets images
$mobileLogo = $assetsUrl . "\icons\phone.svg";
$emailLogo = $assetsUrl . "\icons\email.svg";

$isIGst = $business->stateCode !== $customer->stateCode;

$invoiceLabel = $customSettings[Key::INVOICE_LABEL] ?? "Tax Invoice";
$isIndian = $business->regionCode === "IN";
$isDisplayAmountInWords = $isIndian && $invoiceSettings[Key::IS_DISPlAY_AMOUNT_WORDS];
?>
<?php
$isHeaderFooterEnabled = $appSettings[Key::IS_HEADER_FOOTER_ENABLED] && $template->isHeaderFooterEnable;
$shouldRepeatHeaderFooter = $appSettings[Key::shouldRepeatHeaderFooter];
$isHeaderEnabled = !empty($business->headerImg) && $isHeaderFooterEnabled;
$isFooterEnabled = !empty($business->footerImg) && $isHeaderFooterEnabled;
$withHeaderFooter = "";
if ($isHeaderEnabled) {
    $headerImage = $business->headerImg;
    $footerImage = $business->footerImg;
    $withHeaderFooter = "withHeaderFooter";
}
?>
<?php if ($isHeaderEnabled && $shouldRepeatHeaderFooter) : ?>
    <htmlpageheader name="pdf_header">
        <img src="<?= $headerImage ?>">
    </htmlpageheader>
<?php endif; ?>
<?php if ($isFooterEnabled && $shouldRepeatHeaderFooter) : ?>
    <htmlpagefooter name="pdf_footer">
        <img src="<?= $footerImage ?>">
    </htmlpagefooter>
<?php else : ?>
    <htmlpagefooter name="pdf_footer">
    <?php if($shouldDisplayWatermark):
        // Resolve the image path using Yii2 alias
        $watermarkPath = Yii::getAlias('@backend/web/img/logo/watermark.png');
        ?>
        <div class="absolute-div-content"
             style="position: absolute; top: 96%;  left: 5%; transform: translate(-50%, -50%); color: darkgray; font-size:larger; font-weight: 1200">
                <a href="<?= $watermarkLink ?>"><img src="<?= $watermarkPath ?>" alt="Logo" width="auto" height="50"
                                                     style="padding-bottom: 5px"/></a>
        </div>
        <div class="absolute-div-content"
             style="position: absolute; top: 97%;  left: 43%; transform: translate(-50%, -50%); color: darkgray; font-size:larger; font-weight: 1200">
            <a href="<?=$watermarkLink?>" style="color: darkgray;">www.quotationmaker.app</a>
        </div>
    <?php endif; ?>
    <div class="absolute-div-content"
         style="position: absolute; top: 97%;  left: 87%; transform: translate(-50%, -50%); color: darkgray; font-size:larger; font-weight: 1200">
        Page {PAGENO} of {nb}
    </div>
    </htmlpagefooter>
<?php endif; ?>

<body>
<?php if ($isHeaderEnabled && !$shouldRepeatHeaderFooter) :
    $margin = $template->getMarginSettings();
    $mainMarginTop = $margin->topWithHeaderImage + (6 * $margin->top);
    $absMarginTop = -1 * $margin->top;
    ?>
    <div class="<?= $withHeaderFooter ?>" style="position: absolute; top: <?= $absMarginTop ?>px;  left: 0;">
        <img src="<?= $headerImage ?>" width="100%" height="auto">
    </div>
    <div style="height: <?= $mainMarginTop ?>">&nbsp;</div>
<?php elseif (!$isHeaderEnabled) : ?>
    <div id="header">
        <table>
            <tr>
                <?php
                $alignClass = "leftAlign";
                if (!empty($business->image)) :
                    $alignClass = "centerAlign";
                    ?>
                    <td id="businessLogo">
                        <img src="<?= $business->image ?>" alt="Business Logo" class="business-logo" />
                    </td>
                <?php endif; ?>

                <td id="businessInfo" class="<?= $alignClass ?>">
                    <div class="business-name">
                        <?= $business->name ?>
                    </div>
                    <div class="contact-name">
                        <?= $business->contactName ?>
                    </div>
                    <div class="business-address">
                        <?php if (!empty($business->addressLine1)) : ?>
                            <div class="addressLine">
                                <?= $business->addressLine1 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->addressLine2)) : ?>
                            <div class="addressLine">
                                <?= $business->addressLine2 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->addressLine3)) : ?>
                            <div class="addressLine">
                                <?= $business->addressLine3 ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($business->otherInfo)) : ?>
                            <div class="addressLine">
                                <?= $business->otherInfo ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div>
                        <?php if (!empty($business->phoneNumber)) : ?>
                            <span class="phone-number">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                         class="bi bi-phone" viewBox="0 0 16 16">
                                        <path d="M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z" />
                                        <path d="M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z" />
                                    </svg>
                                    <?= $business->phoneNumber ?>
                                </span>
                        <?php endif; ?>
                        <?php if (!empty($business->email)) : ?>
                            <span class="email">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                         class="bi bi-envelope" viewBox="0 0 16 16">
                                        <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z" />
                                    </svg>
                                    <a class="email" href="mailto: <?= $business->email ?>"><?= $business->email ?></a>
                                </span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($business->stateCode)) : ?>
                        <div class="tax-number">
                            <span class="taxLabel">State Name </span>
                            <span class="taxNumber"><?= $business->stateCode0->name . " - " . $business->stateCode ?></span>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($business->taxNumber)) : ?>
                        <div class="tax-number">
                            <span class="taxLabel"><?= $business->taxLabel ?></span>
                            <span class="taxNumber"><?= $business->taxNumber ?></span>
                        </div>
                    <?php endif; ?>
                </td>
                <td id="topRight" class="text-center">
                    <h1 class="bold"><?= $invoiceLabel ?></h1>
                </td>
            </tr>
        </table>
    </div>
    <hr />
<?php endif; ?>
<main class="<?= $withHeaderFooter ?>" style="">
    <table id="topInfo">
        <tr>
            <td id="clientInfo">
                <div class="bold font-16">BILL TO</div>
                <?php if (!empty($customer->companyName)) : ?>
                    <div class="customer-business-name">
                        <?= $customer->companyName ?>
                    </div>
                <?php endif; ?>
                <?php if (!empty($customer->name)) : ?>
                    <div class="contact-name">
                        <?= $customer->name ?>
                    </div>
                <?php endif; ?>
                <div class="business-address">
                    <?php if (!empty($customer->addressLine1)) : ?>
                        <div class="addressLine">
                            <?= $customer->addressLine1 ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->addressLine2)) : ?>

                        <div class="addressLine">
                            <?= $customer->addressLine2 ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->addressLine3)) : ?>
                        <div class="addressLine">
                            <?= $customer->addressLine3 ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div>
                    <?php if (!empty($customer->phoneNumber)) : ?>
                        <div class="phone-number">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                 class="bi bi-phone" viewBox="0 0 16 16">
                                <path d="M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z" />
                                <path d="M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z" />
                            </svg> <?= $customer->phoneNumber ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($customer->email)) : ?>
                        <div class="email mt-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                 class="bi bi-envelope" viewBox="0 0 16 16">
                                <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z" />
                            </svg>&nbsp; <?= $customer->email ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if (!empty($customer->taxNumber)) : ?>
                    <div class="tax-number">
                        <span class="taxLabel">GSTIN: </span>
                        <span class="taxNumber"><?= $customer->taxNumber ?></span>
                    </div>
                <?php endif; ?>
                <?php if (!empty($customer->stateCode)) : ?>
                    <div class="tax-number">
                        <span class="taxLabel">State Name: </span>
                        <span class="taxNumber"><?= $customer->stateCode0->name . " - " . $customer->stateCode ?></span>
                    </div>
                <?php endif; ?>
                <?php if (!empty($customer->otherInfo)) : ?>
                    <div class="addressLine">
                        <?= nl2br($customer->otherInfo) ?>
                    </div>
                <?php endif; ?>
            </td>
            <?php if (!empty($invoice->deliveryAddress)) : ?>
                <td id="shippingInfo">
                    <div class="customer-business-name">
                        SHIP TO
                    </div>
                    <div class="addressLine">
                        <?= nl2br($invoice->deliveryAddress) ?>
                    </div>
                </td>
            <?php endif; ?>
            <td class="invoiceInfoParent" width="275">
                <table id="invoiceInfo">
                    <?php if ($isHeaderEnabled) : ?>
                        <tr>
                            <td id="topRight" colspan="2">
                                <h1 class="bold"><?= $invoiceLabel ?></h1>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" height="5px">&nbsp;</td>
                        </tr>
                    <?php endif; ?>
                    <tr>
                        <td class="bold noMargin noPadding">Invoice#</td>
                        <td id="quoteNumber  noMargin noPadding"><?= $invoice->invoiceNumber ?> </td>
                    </tr>

                    <tr>
                        <td class="bold">Invoice Date:</td>
                        <td><?= $formatter->asDate($invoice->invoiceDate) ?></td>
                    </tr>

                    <?php if (!empty($invoice->poNumber)) : ?>
                        <tr>
                            <td class="bold">PO#</td>
                            <td id="quoteNumber"><?= $invoice->poNumber ?> </td>
                        </tr>
                    <?php endif; ?>

                    <?php if (!empty($invoice->dueDate)) : ?>
                        <tr>
                            <td class="bold">Due Date:</td>
                            <td><?= $formatter->asDate($invoice->dueDate) ?></td>
                        </tr>
                    <?php endif; ?>
                    <?php if (!empty($otherInfo)) : ?>
                        <?php /** @var \common\models\enum\OtherInfo $info */
                        foreach ($otherInfo as $info) : ?>
                            <tr>
                                <td class="bold"><?= $info->label ?></td>
                                <td><?= $info->value ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </table>
            </td>
        </tr>
    </table>
    <?php if (!empty($topMessage)) : ?>
        <div id="salutation">
            <div class="line2"><span><?= nl2br($topMessage) ?></span></div>
        </div>
    <?php endif; ?>

    <?php
    $colspan = 5;
    $rowspan = 4;
    $displaySubTotal = false;
    $displayDiscount = false;
    $displayDiscountColumn = false;
    $displayTaxColumn = false;
    $displayTax = false;
    if ($discountSettings !== Key::NO_DISCOUNT && $invoice->totalDiscountAmount > 0) {
        $displaySubTotal = true;
        $rowspan++;
    }

    if ($discountSettings !== Key::NO_DISCOUNT && $invoice->totalDiscountAmount > 0) {
        $displayDiscount = true;
        $displayDiscountColumn = true;
        $rowspan++;
    }

    if ($taxSettings !== Key::NO_TAX) {
        $displayTax = true;
        if ($taxSettings === Key::PER_ITEM) {
            $displayTaxColumn = true;
        }
        $rowspan++;
    }

    if ($taxSettings === Key::ON_TOTAL && $discountSettings === Key::ON_TOTAL) {
        $displayDiscountColumn = false;
    }

    if ($invoice->otherCharges > 0) {
        $rowspan++;
    }

    if ($invoice->roundOffAmount > 0) {
        $rowspan++;
    }
    if ($invoice->paidAmount > 0) {
        $rowspan += 2;
    }

    ?>
    <table id="invoiceItems" class="topLine">
        <thead>
        <tr class="bottomLine">
            <th class="idx" width="10">#</th>
            <th class="desc" width="320">DESCRIPTION</th>
            <?php if ($isDisplayHsnCode) : $colspan++ ?>
                <th class="left"><?= $appSettings[Key::HSN_CODE_LABEL] ?></th>
            <?php endif; ?>

            <th class="right">QTY</th>

            <th class="right">PRICE</th>
            <?php if ($displayDiscountColumn) : $colspan++ ?>
                <th class="right">DISCOUNT</th>
            <?php endif; ?>
            <?php if ($displayTaxColumn) : $colspan++ ?>
                <th class="right text-wrap">TAXABLE AMOUNT</th>
                <?php if ($isIGst) : $colspan++ ?>
                    <th class="right">IGST</th>
                <?php else : $colspan += 2; ?>
                    <th class="right">CGST</th>
                    <th class="right">SGST</th>
                <?php endif; ?>
            <?php endif; ?>
            <th class="right">TOTAL</th>
        </tr>
        </thead>
        <tbody>
        <?php
        $i = 1;
        foreach ($invoiceItems as $item) :
            $product = $item->product;
            ?>
            <tr>
                <td class="idx">
                    <?= $i++ ?>
                </td>

                <td class="desc">
                    <div class="productName"><?= $product->name ?></div>
                    <?php if ($item->description) : ?>
                        <div class="productDesc"><?= nl2br($item->description) ?></div>
                    <?php endif; ?>
                </td>
                <?php if ($isDisplayHsnCode) : ?>
                    <td class="hsn"><?= $product->HSNCode ?></td>
                <?php endif; ?>

                <td class="qty">
                    <div><?= $item->quantity ?></div>
                    <div class="unit"> <?= $product->unit ?? "" ?></div>
                </td>
                <td class="price"><?= formatAsCurrency($item->price) ?></td>

                <?php if ($displayDiscountColumn) : ?>
                    <td class="discount">
                        <div><?= formatAsCurrency($item->discountAmount) ?>
                        </div>
                        <?php if ($item->discountType === DiscountType::PERCENTAGE) : ?>
                            <div class="percent"><?= $formatter->asDecimal($item->discountPercentage ?? 0, 1) . "%" ?></div>
                        <?php endif; ?>
                    </td>
                <?php endif; ?>
                <?php if ($displayTaxColumn) : ?>
                    <td class="total"><?= formatAsCurrency($item->totalAmount - $item->taxAmount) ?></td>
                    <?php if ($isIGst) : ?>
                        <td class="tax">
                            <div class="amt"><?= formatAsCurrency($item->taxAmount) ?>
                            </div>
                            <div class="percent"><?= $formatter->asDecimal($product->taxPercentage ?? 0, 1) . "%" ?></div>
                        </td>
                    <?php else : ?>
                        <td class="tax">
                            <div class="amt"><?= formatAsCurrency($item->taxAmount / 2) ?>
                            </div>
                            <div class="percent"><?= $formatter->asDecimal(($product->taxPercentage ?? 0) / 2, 1) . "%" ?></div>
                        </td>
                        <td class="tax">
                            <div class="amt"><?= formatAsCurrency($item->taxAmount / 2) ?>
                            </div>
                            <div class="percent"><?= $formatter->asDecimal(($product->taxPercentage ?? 0) / 2, 1) . "%" ?></div>
                        </td>
                    <?php endif; ?>
                <?php endif; ?>

                <td class="total"><?= formatAsCurrency($item->totalAmount) ?></td>
            </tr>
        <?php endforeach; ?>
        <tr>
            <td colspan="<?= $colspan ?>" class="noBorder" style="height: 1px;margin: 0;padding: 0"></td>
        </tr>
        <tr>
            <td rowspan="<?= $rowspan ?>" colspan="<?= $colspan - 3 ?>" class="noColor"
                style="vertical-align: bottom; text-align: left">
                <?php if ($isDisplayAmountInWords) : ?>
                    <div class="bold text-left"> AMOUNT IN WORDS:</div>
                    <span class="text-left"> <?= amountToWordsINR($invoice->totalAmount) ?></span>
                <?php endif; ?>
            </td>
        </tr>
        <?php if ($displaySubTotal) : ?>
            <tr class="topLine">
                <td colspan="2" class="fixedHeight leftAlign subtotal">SUB TOTAL</td>
                <td class="total fixedHeight subtotal">
                    <?= formatAsCurrency($invoice->subTotalAmount) ?>
                </td>
            </tr>
        <?php endif; ?>

        <?php if ($displayDiscount) : ?>
            <tr>
                <td colspan="2" class="fixedHeight leftAlign">DISCOUNT
                    <?php if ($invoice->discountType === DiscountType::PERCENTAGE && $invoice->discountPercentage > 0) : ?>
                        (<?= $formatter->asDecimal($invoice->discountPercentage, 2) ?>%)
                    <?php endif; ?>
                </td>
                <td class="total fixedHeight">
                    <?= "- " . formatAsCurrency($invoice->totalDiscountAmount) ?>
                </td>
            </tr>
        <?php endif; ?>

        <tr class="topLine">
            <td colspan="2" class="fixedHeight leftAlign subtotal topLine">TAXABLE AMOUNT</td>
            <td class="total fixedHeight subtotal topLine">
                <?= formatAsCurrency($invoice->totalAmount - $invoice->totalTaxAmount - $invoice->otherCharges - $invoice->otherChargesTaxAmount + $invoice->roundOffAmount) ?>
            </td>
        </tr>

        <?php if ($displayTax) : ?>

            <?php if ($isIGst) : ?>
                <tr>
                    <td colspan="2" class="fixedHeight leftAlign"> IGST
                        <?php if ($taxSettings !== Key::PER_ITEM) : ?>
                            (<?= $formatter->asDecimal($invoice->taxPercentage, 2) ?>%)
                        <?php endif; ?>
                    </td>
                    <td class="total fixedHeight">
                        <?= formatAsCurrency($invoice->totalTaxAmount) ?>
                    </td>
                </tr>
            <?php else : ?>
                <tr>
                    <td colspan="2" class="fixedHeight leftAlign"> CGST
                        <?php if ($taxSettings !== Key::PER_ITEM) : ?>
                            (<?= $formatter->asDecimal($invoice->taxPercentage / 2, 2) ?>%)
                        <?php endif; ?>
                    </td>
                    <td class="total fixedHeight">
                        <?= formatAsCurrency($invoice->totalTaxAmount / 2) ?>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" class="fixedHeight leftAlign"> SGST
                        <?php if ($taxSettings !== Key::PER_ITEM) : ?>
                            (<?= $formatter->asDecimal($invoice->taxPercentage / 2, 2) ?>%)
                        <?php endif; ?>
                    </td>
                    <td class="total fixedHeight">
                        <?= formatAsCurrency($invoice->totalTaxAmount / 2) ?>
                    </td>
                </tr>
            <?php endif; ?>


        <?php endif; ?>
        <?php if ($invoice->otherCharges > 0) : ?>
            <tr>
                <td colspan="2" class="fixedHeight leftAlign"> <?= $invoice->otherChargesLabel ?? "Other Charges" ?>
                    <?php if ($invoice->isOtherChargesTaxable && $invoice->otherChargesTaxPercentage > 0) : ?><br />
                        <span class="text-muted"> (<?= $formatter->asDecimal($invoice->otherChargesTaxPercentage, 2) ?>%) </span>
                    <?php endif; ?>
                </td>
                <td class="total fixedHeight">
                    <?= formatAsCurrency($invoice->otherCharges) ?>
                    <?php if ($invoice->isOtherChargesTaxable && $invoice->otherChargesTaxPercentage > 0) : ?>
                        <br />
                        <span class="text-muted"> <?= formatAsCurrency($invoice->otherChargesTaxAmount) ?> </span>
                    <?php endif; ?>
                </td>
            </tr>
        <?php endif; ?>

        <?php if ($invoice->roundOffAmount > 0) : ?>
            <tr>
                <td colspan="2" class="fixedHeight leftAlign"> Round-off
                </td>
                <td class="total fixedHeight">
                    <?= "- " . formatAsCurrency($invoice->roundOffAmount) ?>
                </td>
            </tr>
        <?php endif; ?>

        <?php if ($invoice->paidAmount > 0) : ?>
            <tr class="noBorder topLine">
                <td colspan="2" class="total bold leftAlign">TOTAL
                </td>
                <td class="total">
                    <?= formatAsCurrency($invoice->totalAmount) ?>
                </td>
            </tr>
            <tr>
                <td colspan="2" class="total leftAlign">PAID</td>
                <td class="total fixedHeight nowrap">
                    <?= "- " . formatAsCurrency($invoice->paidAmount) ?>
                </td>
            </tr>
            <tr>
                <td colspan="2" class="grand total bold leftAlign doubleLine">BALANCE DUE</td>
                <td class="grand total bold doubleLine">
                    <?= formatAsCurrency($invoice->dueAmount) ?>
                </td>
            </tr>
        <?php else : ?>
            <tr>
                <td colspan="2" class="grand total bold leftAlign doubleLine">GRAND TOTAL</td>
                <td class="grand total bold doubleLine">
                    <?= formatAsCurrency($invoice->totalAmount) ?>
                </td>
            </tr>
        <?php endif; ?>


        </tbody>
    </table>

    <?php if (!empty($bottomMessage)): ?>
        <div id="closingNote">
            <p><br /><?= nl2br($bottomMessage) ?></p>
        </div>
    <?php endif; ?>

    <?php if ($isPagebreakBeforeTerms) : ?>
        <pagebreak />
    <?php endif; ?>
    <table style="margin-top: 20px;page-break-inside: avoid;margin-bottom: 5px;">
        <tr>
            <?php if ($terms && count($terms) > 0) : ?>
                <td style="text-align: left;vertical-align: top;">
                    <div class="term-title">Terms & Conditions:</div>
                    <ul id="terms">
                        <?php foreach ($terms as $item) : ?>
                            <li class="term">
                                <?= nl2br($item->text) ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </td>
            <?php endif; ?>
            <?php if ($isDisplayBankDetails && !empty($business->bankInfo)) : ?>
                <td width="35%" style="text-align:left; vertical-align:top;">
                    <div class="">
                        <div class="term-title"><?= Yii::t('template-default', 'Payment Instructions') ?></div>
                        <div id="payment-info" class="term">
                            <?= nl2br($business->bankInfo) ?>
                        </div>
                    </div>
                </td>
            <?php endif; ?>

            <?php
            $colsWithPayment = 1;
            if ($terms && count($terms) > 0) {
                echo "</tr> <tr>";
                if ($isDisplayBankDetails || $isDisplayUpiDetails) {
                    $colsWithPayment = 2;
                }
            } ?>
            <?php if ($isDisplaySignatureBlock): ?>
            <td width="" colspan="<?= $colsWithPayment ?>" style="text-align: right;">
                <div id="signBlock">
                    <p class="company-name">For, <?= strtoupper($business->name) ?></p>
                    <div class="signatureSpace" style="height: 60px; text-align:right;">
                        <?php if (!empty($signatureImg)) : ?>
                                <img src="<?= $signatureImg ?>" class="signImage"
                                     style="height: 60px; text-align: right;padding:20px"/>&nbsp;
                        <?php else : ?>
                            <br />
                            <br />
                            <br />
                            <br />
                            <br />
                        <?php endif; ?>
                    </div>
                        <div class="signature"
                             style=" text-align: right;margin-bottom: 10px;"><?= Yii::t('template-default', 'AUTHORIZED SIGNATURE') ?></div>
                </div>
            </td>
            <?php endif; ?>
        </tr>
    </table>

</main>

</body>