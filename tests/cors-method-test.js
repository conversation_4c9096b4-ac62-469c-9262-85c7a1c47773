/**
 * CORS HTTP Method Test Script
 * 
 * This script specifically tests the HTTP methods that were previously blocked
 * by the root .htaccess file to ensure CORS is working correctly.
 * 
 * Run with: node tests/cors-method-test.js
 */

const https = require('https');
const http = require('http');

// Configuration
const API_BASE_URL = 'http://localhost/api'; // Adjust this to your API URL
const TEST_ORIGIN = 'http://localhost:3000';

// HTTP methods to test (these were previously blocked)
const HTTP_METHODS = [
    'GET',
    'POST', 
    'PUT',
    'PATCH',
    'DELETE',
    'OPTIONS'
];

/**
 * Test a specific HTTP method
 */
function testHttpMethod(method, callback) {
    const url = `${API_BASE_URL}/v1/base-api/health-check`;
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname,
        method: method,
        headers: {
            'Origin': TEST_ORIGIN,
            'Content-Type': 'application/json'
        }
    };

    // Add body for methods that typically have one
    let postData = '';
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
        postData = JSON.stringify({ test: 'data' });
        options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = client.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
            responseData += chunk;
        });
        
        res.on('end', () => {
            const corsHeaders = {
                'access-control-allow-origin': res.headers['access-control-allow-origin'],
                'access-control-allow-credentials': res.headers['access-control-allow-credentials'],
                'access-control-allow-methods': res.headers['access-control-allow-methods'],
                'access-control-allow-headers': res.headers['access-control-allow-headers']
            };

            callback(null, {
                method,
                statusCode: res.statusCode,
                statusMessage: res.statusMessage,
                corsHeaders,
                responseData: responseData.substring(0, 200), // First 200 chars
                success: res.statusCode < 400 && corsHeaders['access-control-allow-origin']
            });
        });
    });

    req.on('error', (err) => {
        callback(err, { method, error: err.message });
    });

    // Send body data for applicable methods
    if (postData) {
        req.write(postData);
    }
    
    req.end();
}

/**
 * Test preflight OPTIONS request specifically
 */
function testPreflightOptions(callback) {
    const url = `${API_BASE_URL}/v1/base-api/health-check`;
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname,
        method: 'OPTIONS',
        headers: {
            'Origin': TEST_ORIGIN,
            'Access-Control-Request-Method': 'DELETE', // Test the previously blocked method
            'Access-Control-Request-Headers': 'Content-Type, AUTH_TOKEN'
        }
    };

    const req = client.request(options, (res) => {
        const corsHeaders = {
            'access-control-allow-origin': res.headers['access-control-allow-origin'],
            'access-control-allow-methods': res.headers['access-control-allow-methods'],
            'access-control-allow-headers': res.headers['access-control-allow-headers'],
            'access-control-max-age': res.headers['access-control-max-age']
        };

        callback(null, {
            method: 'OPTIONS (Preflight)',
            statusCode: res.statusCode,
            corsHeaders,
            success: res.statusCode === 200 && corsHeaders['access-control-allow-origin']
        });
    });

    req.on('error', (err) => {
        callback(err, { method: 'OPTIONS (Preflight)', error: err.message });
    });

    req.end();
}

/**
 * Run all tests
 */
async function runTests() {
    console.log('🧪 Testing HTTP Methods for CORS Compatibility');
    console.log('=' .repeat(60));
    console.log(`API URL: ${API_BASE_URL}`);
    console.log(`Test Origin: ${TEST_ORIGIN}`);
    console.log('');

    console.log('📋 Testing HTTP Methods (Previously Blocked):');
    console.log('-'.repeat(50));
    
    // Test each HTTP method
    for (const method of HTTP_METHODS) {
        await new Promise((resolve) => {
            testHttpMethod(method, (err, result) => {
                if (err) {
                    console.log(`❌ ${method.padEnd(7)}: ERROR - ${err.message}`);
                } else if (result.success) {
                    console.log(`✅ ${method.padEnd(7)}: SUCCESS (${result.statusCode})`);
                    if (result.corsHeaders['access-control-allow-origin']) {
                        console.log(`   CORS Origin: ${result.corsHeaders['access-control-allow-origin']}`);
                    }
                } else {
                    console.log(`❌ ${method.padEnd(7)}: FAILED (${result.statusCode} ${result.statusMessage})`);
                    if (result.statusCode === 405) {
                        console.log(`   ⚠️  Method Not Allowed - Check .htaccess configuration`);
                    }
                    if (result.statusCode === 403) {
                        console.log(`   ⚠️  Forbidden - Check Apache security rules`);
                    }
                }
                console.log('');
                resolve();
            });
        });
    }

    // Test preflight request specifically
    console.log('🚀 Testing Preflight OPTIONS Request:');
    console.log('-'.repeat(50));
    
    await new Promise((resolve) => {
        testPreflightOptions((err, result) => {
            if (err) {
                console.log(`❌ Preflight: ERROR - ${err.message}`);
            } else if (result.success) {
                console.log(`✅ Preflight: SUCCESS (${result.statusCode})`);
                console.log(`   CORS Origin: ${result.corsHeaders['access-control-allow-origin']}`);
                console.log(`   Allowed Methods: ${result.corsHeaders['access-control-allow-methods']}`);
                console.log(`   Allowed Headers: ${result.corsHeaders['access-control-allow-headers']}`);
            } else {
                console.log(`❌ Preflight: FAILED (${result.statusCode})`);
            }
            console.log('');
            resolve();
        });
    });

    console.log('🎯 Test Summary:');
    console.log('-'.repeat(50));
    console.log('✅ = Method working correctly with CORS');
    console.log('❌ = Method blocked or CORS issue detected');
    console.log('');
    console.log('Key Issues to Check:');
    console.log('• 405 Method Not Allowed = .htaccess blocking methods');
    console.log('• 403 Forbidden = Apache security rules too strict');
    console.log('• Missing CORS headers = Application CORS config issue');
    console.log('');
    console.log('If PUT, PATCH, DELETE show as blocked:');
    console.log('1. Check root .htaccess <LimitExcept> directive');
    console.log('2. Verify Apache mod_rewrite rules');
    console.log('3. Ensure CORS config matches allowed methods');
}

// Run the tests
runTests().catch(console.error);
