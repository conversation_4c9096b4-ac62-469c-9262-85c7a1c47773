/**
 * CORS Verification Script
 * 
 * This Node.js script tests the CORS configuration of the Quotation Pro API
 * Run with: node tests/cors-verification.js
 */

const https = require('https');
const http = require('http');

// Configuration
const API_BASE_URL = 'http://localhost/api'; // Adjust this to your API URL
const TEST_ORIGINS = [
    'http://localhost:3000',
    'https://localhost:3000',
    'https://quotationmaker.app',
    'https://example.com', // Should work with wildcard
    'https://random-domain.com' // Should also work with wildcard
];

/**
 * Make a request to test CORS headers
 */
function testCorsRequest(origin, callback) {
    const url = `${API_BASE_URL}/v1/base-api/health-check`;
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname,
        method: 'GET',
        headers: {
            'Origin': origin,
            'Content-Type': 'application/json'
        }
    };

    const req = client.request(options, (res) => {
        const corsHeaders = {
            'access-control-allow-origin': res.headers['access-control-allow-origin'],
            'access-control-allow-credentials': res.headers['access-control-allow-credentials'],
            'access-control-allow-methods': res.headers['access-control-allow-methods'],
            'access-control-allow-headers': res.headers['access-control-allow-headers'],
            'access-control-max-age': res.headers['access-control-max-age']
        };

        callback(null, {
            origin,
            statusCode: res.statusCode,
            corsHeaders,
            success: res.statusCode === 200 && corsHeaders['access-control-allow-origin']
        });
    });

    req.on('error', (err) => {
        callback(err, { origin, error: err.message });
    });

    req.end();
}

/**
 * Test OPTIONS preflight request
 */
function testPreflightRequest(origin, callback) {
    const url = `${API_BASE_URL}/v1/base-api/health-check`;
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname,
        method: 'OPTIONS',
        headers: {
            'Origin': origin,
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type, AUTH_TOKEN'
        }
    };

    const req = client.request(options, (res) => {
        const corsHeaders = {
            'access-control-allow-origin': res.headers['access-control-allow-origin'],
            'access-control-allow-methods': res.headers['access-control-allow-methods'],
            'access-control-allow-headers': res.headers['access-control-allow-headers']
        };

        callback(null, {
            origin,
            statusCode: res.statusCode,
            corsHeaders,
            success: res.statusCode === 200 && corsHeaders['access-control-allow-origin']
        });
    });

    req.on('error', (err) => {
        callback(err, { origin, error: err.message });
    });

    req.end();
}

/**
 * Run all tests
 */
async function runTests() {
    console.log('🧪 Testing CORS Configuration for Quotation Pro API');
    console.log('=' .repeat(60));
    console.log(`API URL: ${API_BASE_URL}`);
    console.log('');

    // Test simple GET requests
    console.log('📋 Testing Simple GET Requests:');
    console.log('-'.repeat(40));
    
    for (const origin of TEST_ORIGINS) {
        await new Promise((resolve) => {
            testCorsRequest(origin, (err, result) => {
                if (err) {
                    console.log(`❌ ${origin}: ERROR - ${err.message}`);
                } else if (result.success) {
                    console.log(`✅ ${origin}: SUCCESS`);
                    console.log(`   Status: ${result.statusCode}`);
                    console.log(`   CORS Origin: ${result.corsHeaders['access-control-allow-origin']}`);
                } else {
                    console.log(`❌ ${origin}: FAILED`);
                    console.log(`   Status: ${result.statusCode}`);
                    console.log(`   CORS Origin: ${result.corsHeaders['access-control-allow-origin'] || 'Not set'}`);
                }
                console.log('');
                resolve();
            });
        });
    }

    // Test preflight requests
    console.log('🚀 Testing Preflight OPTIONS Requests:');
    console.log('-'.repeat(40));
    
    for (const origin of TEST_ORIGINS.slice(0, 2)) { // Test only first 2 origins for preflight
        await new Promise((resolve) => {
            testPreflightRequest(origin, (err, result) => {
                if (err) {
                    console.log(`❌ ${origin}: ERROR - ${err.message}`);
                } else if (result.success) {
                    console.log(`✅ ${origin}: SUCCESS`);
                    console.log(`   Status: ${result.statusCode}`);
                    console.log(`   CORS Origin: ${result.corsHeaders['access-control-allow-origin']}`);
                    console.log(`   Allowed Methods: ${result.corsHeaders['access-control-allow-methods']}`);
                } else {
                    console.log(`❌ ${origin}: FAILED`);
                    console.log(`   Status: ${result.statusCode}`);
                }
                console.log('');
                resolve();
            });
        });
    }

    console.log('🎯 Test Summary:');
    console.log('-'.repeat(40));
    console.log('✅ = CORS working correctly');
    console.log('❌ = CORS issue detected');
    console.log('');
    console.log('With wildcard origin (*), ALL origins should work.');
    console.log('If you see failures, check:');
    console.log('1. API server is running');
    console.log('2. BaseApiController CORS configuration');
    console.log('3. No conflicting web server CORS headers');
    console.log('4. Access-Control-Allow-Credentials should be false');
}

// Run the tests
runTests().catch(console.error);
